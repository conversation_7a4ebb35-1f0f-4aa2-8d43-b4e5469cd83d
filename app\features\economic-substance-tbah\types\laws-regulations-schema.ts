import { z } from "zod";
import { stringBoolean } from "~/lib/utilities/zod-validators";

export const lawsRegulationsSchema = z.object({
  isCompliantWithBahamasLawsAndRegulations: stringBoolean(),
})

export type LawsRegulationsSchemaType = z.infer<typeof lawsRegulationsSchema>

export function getLawsRegulationsDefaultValues(data: LawsRegulationsSchemaType | undefined): LawsRegulationsSchemaType {
  return {
    isCompliantWithBahamasLawsAndRegulations: (data?.isCompliantWithBahamasLawsAndRegulations ?? undefined) as "true" | "false", // bypass to set as undefined the first status

  };
}
