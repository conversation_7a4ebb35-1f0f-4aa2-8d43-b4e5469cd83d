import { But<PERSON> } from "@netpro/design-system";
import { Link, useLoaderData } from "@remix-run/react";
import { createColumnHelper } from "@tanstack/react-table";
import { Filter } from "lucide-react";
import { useMemo } from "react";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormSearch } from "~/components/FormSearch";
import { searchSchema } from "~/features/reports/schemas/search-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { useFormatDate } from "~/lib/hooks/useFormatDate";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import type { ReportDTO } from "~/services/api-generated";
import { managementGetReportsByType } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Reports",
    to: "/reports",
  },
  title: "Reports",
}

export const loader = makeEnhancedLoader(async ({ json, request, queryString }) => {
  await middleware(["auth"], request);
  const { pageSize, pageNumber } = await getFilterParams({ request });
  const schemaData = searchSchema.safeParse(queryString).data
  const reportsResponse = await managementGetReportsByType({
    headers: await authHeaders(request),
    query: {
      ReportTypes: ["CompaniesWithoutSubmissions", "ContactsInfo", "SubmissionsNotPaid"],
      PageSize: pageSize,
      PageNumber: pageNumber,
      SearchTerm: schemaData?.search,
    },
  });

  return json({
    reportsData: reportsResponse.data,
  })
  /*
   * the ReportTypes: ["CompaniesWithoutSubmissions", "ContactsInfo", "SubmissionsNotPaid"] are specific
   * to authorize: ["str.management-information"] in the future if more report types need to be added then
   *  a separate page should be made for those (probably based on jurisdiction) are added we need to make a submenu
   */
}, { authorize: ["str.management-information"] })

const columnHelper = createColumnHelper<ReportDTO>()

export default function ReportsLayout(): JSX.Element {
  const formatDate = useFormatDate();
  const { reportsData } = useLoaderData<typeof loader>()
  const { formMethods } = useFilterForm(searchSchema)
  //
  const columns = useMemo(() => ([
    columnHelper.display({ id: "name", header: "Report Name", cell: props => props.row.original.reportName }),
    columnHelper.display({ id: "createdAt", header: "Created At", cell: props => props.row.original.createdAt && formatDate(props.row.original.createdAt) }),
    columnHelper.display({ id: "download", header: "Download Link", cell: props => (
      <Button
        asChild
        size="sm"
        variant="outline"
      >
        <Link to={`/reports/${props.row.original.id}/download?filename=${props.row.original.filename}`} reloadDocument>
          <span className="text-xs font-semibold">Download</span>
        </Link>
      </Button>
    ) }),
  ]), [formatDate])

  return (
    <CardContainer>
      <Form formMethods={formMethods}>
        <FilterRow>
          <div className="col-span-full flex flex-row items-center gap-2">
            <FormSearch
              name="search"
              formItemProps={{ className: "w-full" }}
              inputProps={{ placeholder: "Search reports" }}
            />
            <Button size="sm" className="gap-1.5" type="submit">
              <Filter size={14} />
              Apply Filter(s)
            </Button>
          </div>
        </FilterRow>
      </Form>
      <EnhancedTableContainer>
        <EnhancedTable data={reportsData?.data} columns={columns} rowId="id" totalItems={reportsData?.totalItemCount} />
      </EnhancedTableContainer>
    </CardContainer>
  )
}
