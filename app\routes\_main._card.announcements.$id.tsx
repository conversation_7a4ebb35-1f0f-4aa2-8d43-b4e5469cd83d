import {
  unstable_createM<PERSON>oryU<PERSON><PERSON><PERSON><PERSON><PERSON> as createMemoryUpload<PERSON>and<PERSON>,
  unstable_parseMultipartFormData as parseMultipartFormData,
} from "@remix-run/node";
import { Outlet } from "@remix-run/react";
import AnnouncementForm from "~/features/announcements/components/forms/AnnouncementForm";
import type { AnnouncementSchemaType } from "~/features/announcements/schema/announcement-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import type { CreateUpdateAnnouncementDTO, ManagementCreateAnnouncementDocumentData } from "~/services/api-generated";
import { getJurisdictions, getMasterClients, managementAnnouncementGetById, managementAnnouncementUpdate, managementCreateAnnouncementDocument, managementDeleteAnnouncementDocument } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Edit Announcement",
    to: "/announcements/edit",
  },
  title: "Edit Announcement",
}

export const loader = makeEnhancedLoader(async ({ json, request, params }) => {
  await middleware(["auth"], request);
  const { id } = params
  if (!id) {
    throw new Response("The Id is required to fetch the announcement data", { status: 400 })
  }

  const { data: jurisdictions, error: jurisdictionError } = await getJurisdictions({ headers: await authHeaders(request), query: { pageNumber: 1, pageSize: 100 } });
  const { data: masterClients, error: masterClientError } = await getMasterClients({ headers: await authHeaders(request), query: { pageNumber: 1, pageSize: 100 } })
  const { data: announcement, error: announcementError } = await managementAnnouncementGetById({ headers: await authHeaders(request), path: { announcementId: id } })

  if (jurisdictionError) {
    throw new Response("Failed to fetch jurisdictions", { status: 500 });
  }

  if (masterClientError) {
    throw new Response("Failed to fetch master clients", { status: 500 });
  }

  if (announcementError) {
    throw new Response("Failed to fetch announcements", { status: 500 })
  }

  if (!jurisdictions.data) {
    throw new Response("Jurisdictions not found", { status: 404 });
  }

  if (!masterClients.data) {
    throw new Response("Master clients not found", { status: 404 });
  }

  if (!announcement) {
    throw new Response("Announcement not found", { status: 404 });
  }

  const announcementData = announcement instanceof Blob ? JSON.parse(await announcement.text()) : announcement;

  jurisdictions.data.unshift({ id: "all", name: "All" })

  return json({
    announcementData,
    options: {
      jurisdictions: jurisdictions.data,
      masterClients: masterClients.data,
    },
  })
}, { authorize: ["announcements.view", "announcements.create"] })

type AnnouncementCreationFormData = AnnouncementSchemaType & { sendAt: string, masterClientCodes: string }
export const action = makeEnhancedAction(async ({ params, request, redirect, setNotification }) => {
  await middleware(["auth"], request)
  const { id } = params
  if (!id) {
    throw new Response("The Id is required to fetch the announcement data", { status: 400 })
  }

  const maxPartSize = 5 * 1024 * 1024; // 5MB in bytes
  const uploadHandler = createMemoryUploadHandler({
    maxPartSize,
  });
  const formData = await parseMultipartFormData(request, uploadHandler);
  const { subject, emailSubject, sendNow, sendAt, sendToAllMasterClients, masterClientCodes, jurisdictionId, body }
   = Object.fromEntries(formData) as unknown as AnnouncementCreationFormData
  const createAnnouncementBody: CreateUpdateAnnouncementDTO = {
    subject,
    emailSubject,
    sendNow: sendNow === "true",
    sendToAllMasterClients: sendToAllMasterClients === "true",
    body,
  }
  const files = formData.getAll("files") as File[];

  if (!createAnnouncementBody.sendNow) {
    createAnnouncementBody.sendAt = sendAt
  }

  if (createAnnouncementBody.sendToAllMasterClients) {
    if (jurisdictionId === "all") {
      createAnnouncementBody.sendToAllJurisdictions = true
    } else {
      createAnnouncementBody.jurisdictionId = jurisdictionId
    }
  }

  if (!createAnnouncementBody.sendToAllMasterClients) {
    createAnnouncementBody.masterClientCodes = JSON.parse(masterClientCodes)
  }

  if (files.length > 0) {
    createAnnouncementBody.includeAttachments = true
  }

  const headers = await authHeaders(request);
  const { data: announcement, error: announcementError } = await managementAnnouncementGetById({ headers, path: { announcementId: id } })
  const { data: announcementId, error } = await managementAnnouncementUpdate({ headers, path: { announcementId: id }, body: createAnnouncementBody })

  if (announcementError) {
    throw new Response(announcementError.exceptionMessage as string, { status: 500 })
  }

  if (error) {
    throw new Response(error.exceptionMessage as string, { status: 500 })
  }

  const announcementData = announcement instanceof Blob ? JSON.parse(await announcement.text()) : announcement;
  const announcementIdStr = announcementId instanceof Blob ? await announcementId.text() : announcementId;

  if (announcementData?.documents) {
    const documentsRequest = announcementData.documents.map((document: { id: string }) =>
      managementDeleteAnnouncementDocument({ headers, path: { announcementDocumentId: document.id! } }))
    await Promise.all(documentsRequest);
  }

  if (files.length > 0) {
    const formattedFiles: ManagementCreateAnnouncementDocumentData["body"][] = files.map((file, index) => ({
      File: file,
      UploadComplete: index === files.length - 1,
    }));
    // Create an array of promises for all file uploads
    const uploadPromises = formattedFiles.map(formattedFile =>
      managementCreateAnnouncementDocument({
        path: { announcementId: announcementIdStr },
        body: formattedFile,
      }),
    );

    // Wait for all uploads to complete concurrently
    await Promise.all(uploadPromises);
  }

  setNotification({ title: "Announcement updated successfully", variant: "success" })

  return redirect("/announcements")
}, {
  authorize: ["announcements.create"],
});

export type EditAnnouncementLoaderData = typeof loader;

export default function AnnouncementDetails() {
  return (
    <>
      <AnnouncementForm type="update" />
      <Outlet />
    </>
  );
}
