import { RFIStatus } from "~/features/rfi/types/rfi-types";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import type { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { middleware } from "~/lib/middlewares.server";
import { managementCancelRfi, managementGetSubmissionRfiDetails } from "~/services/api-generated";

type ActionArgs = Parameters<Parameters<typeof makeEnhancedAction>[0]>[0];

export async function getRfiCancelAction(route: string, { request, redirect, params, setNotification }: ActionArgs) {
  await middleware(["auth"], request);

  const { id } = params;

  if (!id) {
    throw new Response("Not Found", { status: 404 });
  }

  const headers = await authHeaders(request);
  const rfiDetails = await managementGetSubmissionRfiDetails({
    path: { submissionId: id },
    headers,
  });
  const activeRfi = rfiDetails?.data?.requestsForInformation?.find(rfi => rfi.status === RFIStatus.ACTIVE);

  if (!activeRfi) {
    setNotification({ title: "No active request for information found", variant: "error" });

    return redirect(`/${route}/${id}`);
  }

  const { error: cancelRfiError } = await managementCancelRfi({
    path: { requestForInformationId: activeRfi.id! },
    headers,
  })

  if (cancelRfiError) {
    setNotification({ title: cancelRfiError.exceptionMessage as string, variant: "error" })
  } else {
    setNotification({ title: "Request for Information cancelled successfully", variant: "success" })
  }

  return redirect(`/${route}/${id}`)
}
