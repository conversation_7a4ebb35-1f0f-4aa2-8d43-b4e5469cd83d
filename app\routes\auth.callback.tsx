import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import type { ReactNode } from "react";
import { Outlet } from "@remix-run/react";
import { getAccessToken } from "~/lib/auth/utils/authentication.server";

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse> {
  return getAccessToken(request, "/auth/application");
}

export default function Layout(): ReactNode {
  return <Outlet />;
}
