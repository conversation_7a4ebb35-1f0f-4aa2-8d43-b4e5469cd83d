import type { ReactNode } from "react";
import { <PERSON><PERSON>, Sheet, She<PERSON><PERSON>onte<PERSON>, SheetPortal } from "@netpro/design-system";
import { useEffect, useState } from "react";
import { PageErrorBoundary } from "~/components/errors/PageErrorBoundary";
import { useDelayedNavigate } from "~/lib/hooks/useDelayedNavigate";

/**
 * OverlayErrorBoundary is a universal error boundary component that will
 * catch any error that occurs overlay views
 */
export function OverlayErrorBoundary({ children }: { children?: ReactNode }) {
  const [sheetContainer, setSheetContainer] = useState<Element | null>(null);
  const [open, setOpen] = useState(true);
  const delayedNavigate = useDelayedNavigate({
    beforeDelay() {
      setOpen(false);
    },
  });

  useEffect(() => {
    // This is a workaround to prevent hydration errors.
    setSheetContainer(document.body);
  }, []);

  return (
    <Sheet open={open} onOpenChange={open => !open && delayedNavigate(-1)}>
      <SheetPortal container={sheetContainer}>
        <SheetContent side="right" className="[&>button]:hidden" onPointerDownOutside={e => e.preventDefault()}>
          <div className="h-full flex flex-col text-center">
            <PageErrorBoundary>
              {children || (
                <Button size="sm" onClick={() => delayedNavigate(-1)}>
                  Close overlay
                </Button>
              )}
            </PageErrorBoundary>
          </div>
        </SheetContent>
      </SheetPortal>
    </Sheet>
  )
}
