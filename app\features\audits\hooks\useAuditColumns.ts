import { createColumnHelper } from "@tanstack/react-table";
import type { AuditedEntity } from "~/features/audits/api/get-audits";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";

export function useAuditColumns() {
  const formatColDate = useFormatColDate()
  const columnHelper = createColumnHelper<AuditedEntity>();
  const columns = [
    columnHelper.display({
      id: "actionDate",
      header: "Logged At",
      cell: formatColDate("actionDate", { extend: " hh:mma" }),
      sortDescFirst: false,
    }),
    columnHelper.display({
      id: "userName",
      header: "Modified By",
      cell: props => props.row.original.emailAddress ?? props.row.original.userName,
    }),
    columnHelper.display({
      id: "shortDescription",
      header: "Description",
      cell: props => props.row.original.shortDescription,
    }),
  ];

  return { columns }
}
