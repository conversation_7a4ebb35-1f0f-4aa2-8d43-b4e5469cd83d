{
  "compilerOptions": {
    "target": "ES2022",
    "jsx": "react-jsx",
    "lib": ["DOM", "DOM.Iterable", "ES2022", "ES2023.Array"],
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "paths": {
      "~/*": ["./app/*"]
    },
    "resolveJsonModule": true,
    "types": ["@remix-run/node", "vite/client"],
    "allowJs": true,
    "strict": true,

    // Vite takes care of building everything, not tsc.
    "noEmit": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/.server/**/*.ts",
    "**/.server/**/*.tsx",
    "**/.client/**/*.ts",
    "**/.client/**/*.tsx",
    "tailwind.config.mjs"
  ]
}
