import { type FC, type JSX, type Props<PERSON><PERSON><PERSON><PERSON>dren, useContext } from "react";
import type { permissionName } from "~/services/api-generated";
import { ContextUser } from "./ContextUser";

type Props = {
  oneOf: permissionName[]
  fallback?: JSX.Element
} & PropsWithChildren

/**
 * Only renders children if the users possesses one of the permissions defined in `oneOf`
 * otherwise renders fallback/null
 *
 * @example
 * // also see app/routes/_main._card.bo-directors.overview.tsx for an actual implementation
 * <Authorized oneOf={['somePermission']}>
 *  Component only authorized users may see
 * </Authorized>
 */
export const Authorized: FC<Props> = ({ fallback, oneOf, children }) => {
  const { permissions, userId } = useContext(ContextUser)
  const returnValue = fallback ?? null

  if (!userId || !permissions) {
    return returnValue
  }

  const hasPermission = oneOf.some(permission => permissions.includes(permission))

  if (!hasPermission) {
    return returnValue
  }

  return children
}
