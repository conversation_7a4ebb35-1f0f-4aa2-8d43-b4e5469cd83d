import { Button, Dropzone, FileUploader } from "@netpro/design-system";
import { CloudUpload } from "lucide-react";

import * as AlertDialog from "~/components/AlertDialog";
import { usePreserveQueryNavigate } from "~/lib/hooks/usePreserveQueryNavigate";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";

export const action = makeEnhancedAction(async ({ redirect, setNotification }) => {
  setNotification({ title: "Submissions have successfully been marked as paid" })

  return redirect("/economic-substance/payments")
})

export default function EconomicSubstanceSubmissionsPaymentsMarkAsPaid(): JSX.Element {
  const navigate = usePreserveQueryNavigate()

  return (
    <AlertDialog.Root open onOpenChange={() => navigate("/economic-substance/payments/")}>
      <AlertDialog.Portal>
        <AlertDialog.Overlay />
        <AlertDialog.Content>
          <AlertDialog.Title>
            Bulk import payments
          </AlertDialog.Title>
          <AlertDialog.Description className="text-md text-black">
            <FileUploader>
              <Dropzone className="flex h-36 flex-col gap-2 border-gray-300">
                <CloudUpload className="size-10 text-primary" />
                <div className="flex flex-col gap-1">
                  <p>
                    Drag and drop the Microsoft Excel import file here, or click to select files
                  </p>
                </div>
              </Dropzone>
            </FileUploader>
          </AlertDialog.Description>
          <AlertDialog.Footer>
            <AlertDialog.Cancel asChild>
              <Button onClick={() => navigate("/economic-substance/payments")} variant="outline" size="sm">
                Cancel
              </Button>
            </AlertDialog.Cancel>
            <Button type="submit" size="sm">
              Import
            </Button>
          </AlertDialog.Footer>
        </AlertDialog.Content>
      </AlertDialog.Portal>
    </AlertDialog.Root>
  )
}
