import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { <PERSON>ton, FileList, FormControl, FormField, FormItem, FormLabel, FormMessage, Form as NetProForm, RadioGroupItem, SelectItem } from "@netpro/design-system";
import { Form as RemixForm, useLoaderData, useNavigate, useNavigation, useSubmit } from "@remix-run/react";
import { formatISO } from "date-fns";
import { Pencil, X } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { Authorized } from "~/components/Authorized";
import { FormCombobox } from "~/components/FormCombobox";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormInput } from "~/components/FormInput";
import { FormRadioGroup } from "~/components/FormRadioGroup";
import { FormSelect } from "~/components/FormSelect";
import { FormTextarea } from "~/components/FormTextarea";
import { LinkButton } from "~/components/ui/buttons/LinkButton";
import { fileDataToFile } from "~/lib/utilities/files";
import type { EditAnnouncementLoaderData } from "~/routes/_main._card.announcements.$id";
import type { DocumentDTO } from "~/services/api-generated/types.gen";
import { announcementSchema, type AnnouncementSchemaType } from "../../schema/announcement-schema";
import { getDefaultValues } from "../../utilities/announcement-form";
import { FileUploader } from "./FileUploader";

const MAX_FILES = 3

export default function AnnouncementForm({ type }: { type: "create" | "update" }) {
  const loader = useLoaderData<EditAnnouncementLoaderData>()

  if (!loader) {
    throw new Error("Loader data is missing")
  }

  const { options: { jurisdictions, masterClients }, announcementData } = loader
  const submitButtonLabel = type === "create" ? "Create" : "Update"
  const [isReadonly, setIsReadonly] = useState(true)
  // form logic
  const form = useForm<AnnouncementSchemaType>({
    resolver: zodResolver(announcementSchema),
    defaultValues: getDefaultValues(announcementData),
  });
  const { handleSubmit, watch, setValue } = form
  const { sendNow: sendMessageNow, sendToAllMasterClients: sendMessageToMasterClients, files, body } = watch()
  // Calculate dynamic rows based on content length
  const calculateRows = (content: string = ""): number => {
    const minRows = 3;
    const maxRows = 30;
    const charactersPerRow = 120;
    const lineBreaks = (content.match(/\n/g) || []).length;
    const estimatedRows = Math.ceil(content.length / charactersPerRow) + lineBreaks;

    return Math.max(minRows, Math.min(maxRows, estimatedRows));
  };
  const dynamicRows = calculateRows(body);
  // submitting state logic
  const navigate = useNavigate()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "loading" || navigation.state === "submitting"
  const isElementDisabled = isSubmitting || isReadonly
  const submit = useSubmit()

  function onSubmit(values: AnnouncementSchemaType) {
    const formData = new FormData()
    formData.append("subject", values.subject)
    formData.append("emailSubject", values.emailSubject)
    formData.append("sendNow", values.sendNow)
    if (values.sendNow === "false" && (values.scheduledDate && values.scheduledTime)) {
      const [hours, minutes] = values.scheduledTime.split(":").map(Number)
      const scheduledDateTime = new Date(values.scheduledDate)
      scheduledDateTime.setHours(hours, minutes, 0, 0)
      formData.append("sendAt", formatISO(scheduledDateTime))
    }

    formData.append("sendToAllMasterClients", values.sendToAllMasterClients)
    if (values.sendToAllMasterClients === "true") {
      formData.append("jurisdictionId", values.jurisdictionId)
    }

    if (values.sendToAllMasterClients === "false") {
      formData.append("masterClientCodes", JSON.stringify(values.masterClientCodes))
    }

    formData.append("body", values.body)
    const filesData = values.files
    if (filesData && filesData.length > 0) {
      filesData.forEach((fileData) => {
        formData.append("files", fileData)
      });
    }

    submit(formData, { encType: "multipart/form-data", method: "post" })
  }

  // add the option all to the jurisdictions
  const jurisdictionOptions = useMemo(() => (jurisdictions.map(jurisdiction => (
    <SelectItem key={jurisdiction.id} value={jurisdiction.id!}>
      {jurisdiction.name}
    </SelectItem>
  ))), [jurisdictions])
  const masterClientOptions = useMemo(() => (masterClients.map(masterClient => ({
    value: masterClient.code as string,
    label: masterClient.code as string,
  }))), [masterClients])
  // handle change of some fields
  const handleSendMessageNowChange = (value: string) => {
    const formattedValue = value as AnnouncementSchemaType["sendNow"]
    setValue("sendNow", formattedValue)
    if (formattedValue === "true") {
      setValue("scheduledDate", undefined);
      setValue("scheduledTime", "");
    }
  }
  const handleSendMessageToMasterClientsChange = (value: string) => {
    const formattedValue = value as AnnouncementSchemaType["sendToAllMasterClients"]
    setValue("sendToAllMasterClients", formattedValue)
    if (formattedValue === "true") {
      setValue("jurisdictionId", "");
    }

    if (formattedValue === "false") {
      setValue("masterClientCodes", []);
    }
  }

  // Transform documentData to File when loading
  useEffect(() => {
    if (announcementData && announcementData.documents) {
      const fileArray: File[] = []
      announcementData.documents.forEach((doc: { document: DocumentDTO }) => {
        const file = fileDataToFile(doc.document!)
        fileArray.push(file)
      });

      setValue("files", fileArray)
    }
  }, [announcementData, setValue])

  // Set readonly when announcement is sent
  useEffect(() => {
    if (type === "update" && loader.announcementData && loader.announcementData.isSent) {
      setIsReadonly(true)
    }
  }, [loader.announcementData, setIsReadonly, type])

  return (
    <FormProvider {...form}>
      <NetProForm {...form}>
        <RemixForm
          onSubmit={handleSubmit(onSubmit)}
          method="post"
          className="p-2 mx-auto float-left w-full"
          noValidate
        >
          <div className="space-y-7 sm:w-1/2 w-full pb-6">
            <FormInput name="subject" label="Subject*" inputProps={{ disabled: isElementDisabled }} />
            <FormInput name="emailSubject" label="Email Subject*" inputProps={{ disabled: isElementDisabled }} />
            <FormRadioGroup
              name="sendNow"
              label="Send message now?*"
              radioGroupProps={{ disabled: isElementDisabled, onValueChange: handleSendMessageNowChange }}
            >
              <FormItem className="flex items-center space-x-2 space-y-0">
                <FormControl>
                  <RadioGroupItem value="true" />
                </FormControl>
                <FormLabel className="font-normal">
                  Yes
                </FormLabel>
              </FormItem>
              <FormItem className="flex items-center space-x-2 space-y-0">
                <FormControl>
                  <RadioGroupItem value="false" />
                </FormControl>
                <FormLabel className="font-normal">
                  No
                </FormLabel>
              </FormItem>
            </FormRadioGroup>
            {sendMessageNow === "false" && (
              <div className="flex flex-col sm:flex-row sm:gap-4 gap-0">
                <FormDatePicker
                  name="scheduledDate"
                  label="Scheduled Send Date"
                  datePickerProps={{
                    disabled: isElementDisabled,
                    disabledDates: { before: new Date() },
                  } as any}
                  formItemProps={{ className: "w-full" }}
                />
                <FormInput name="scheduledTime" label="Time (UTC)" inputProps={{ type: "time", disabled: isElementDisabled }} />
              </div>
            )}
            <FormRadioGroup
              name="sendToAllMasterClients"
              label="Send message to all Master Client Codes?*"
              radioGroupProps={{ disabled: isElementDisabled, onValueChange: handleSendMessageToMasterClientsChange }}
            >
              <FormItem className="flex items-center space-x-2 space-y-0">
                <FormControl>
                  <RadioGroupItem value="true" />
                </FormControl>
                <FormLabel className="font-normal">
                  Yes
                </FormLabel>
              </FormItem>
              <FormItem className="flex items-center space-x-2 space-y-0">
                <FormControl>
                  <RadioGroupItem value="false" />
                </FormControl>
                <FormLabel className="font-normal">
                  No
                </FormLabel>
              </FormItem>
            </FormRadioGroup>
            {sendMessageToMasterClients === "true"
            && (
              <FormSelect
                name="jurisdictionId"
                label="Jurisdiction*"
                selectProps={{ disabled: isElementDisabled }}
                selectValueProps={{ placeholder: "Select one jurisdiction" }}
                options={jurisdictionOptions}
              />
            )}
            {sendMessageToMasterClients === "false" && (
              <FormCombobox
                name="masterClientCodes"
                label="Master Client"
                comboboxProps={{ disabled: isElementDisabled, multiple: true, placeholder: "Select one or multiple masterclient" }}
                options={masterClientOptions}
              />

            )}
            <FormTextarea
              name="body"
              label="Content*"
              textareaProps={{
                disabled: isElementDisabled,
                rows: dynamicRows,
              }}
            />

            <FormField
              control={form.control}
              name="files"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <>
                      {!isReadonly && (
                        <FileUploader
                          maxFiles={MAX_FILES}
                          files={field.value || []}
                          allowedTypes={["application/pdf"]}
                          setFiles={field.onChange}
                          disabled={isSubmitting}
                        >
                          <div className="flex flex-col gap-1">
                            <p>
                              Drag and drop files here, or click to select files
                            </p>
                            <p className="text-sm text-gray-400">
                              PDF only. File must not be password protected.
                              Files will be automatically uploaded
                            </p>
                          </div>
                        </FileUploader>
                      )}
                      {files && (
                        <FileList
                          files={files}
                          setFiles={field.onChange}
                          canDelete={!isReadonly}
                        />
                      )}
                    </>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

          </div>
          {/* Buttons */}
          <div className="flex justify-end gap-4">
            {!isReadonly && (
              <Authorized oneOf={["announcements.create"]}>
                <Button type="button" disabled={isSubmitting} variant="outline" onClick={() => navigate("/announcements")}>Cancel</Button>
                <Button type="submit" disabled={isSubmitting}>{submitButtonLabel}</Button>
              </Authorized>
            )}
            {isReadonly && (
              <>
                <LinkButton buttonProps={{ disabled: isSubmitting, variant: "outline" }} linkProps={{ to: "/announcements" }}>
                  Close
                </LinkButton>
                {!announcementData?.sentAt && (
                  <>
                    <Authorized oneOf={["announcements.delete"]}>
                      <Button type="button" disabled={isSubmitting} onClick={() => navigate("delete")} variant="destructive">
                        <X className="size-4 mr-2" />
                        Delete
                      </Button>
                    </Authorized>
                    <Authorized oneOf={["announcements.create"]}>
                      <Button type="button" disabled={isSubmitting} onClick={() => setIsReadonly(false)}>
                        <Pencil className="size-4 mr-2" />
                        Edit
                      </Button>
                    </Authorized>
                  </>
                )}
              </>
            )}
          </div>
        </RemixForm>
      </NetProForm>
    </FormProvider>
  );
}
