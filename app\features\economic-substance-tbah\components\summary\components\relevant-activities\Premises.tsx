import { useLoaderData } from "@remix-run/react";
import type { PremisesSchemaType } from "~/features/economic-substance-tbah/types/premises-schema";
import type { PageSlug } from "~/features/economic-substance-tbah/utilities/form-pages";
import { transformBooleanStringToLabel } from "~/features/economic-substance-tbah/utilities/summary";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.submissions.$id.summary";
import { SummaryTable } from "../table/SummaryTable";
import { SummaryTableData } from "../table/SummaryTableData";
import { SummaryTableRow } from "../table/SummaryTableRow";

export function Premises({ page }: { page: PageSlug }) {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { bahamasPremisesOwnership, premises } = submissionData[page] as PremisesSchemaType

  return (
    <div className="space-y-5">
      <h2 className="text-blue-500 font-thin mb-4 text-lg">3. Premises</h2>
      <SummaryTable>
        <tbody>
          <SummaryTableRow>
            <SummaryTableData>
              Does the entity own any premises in Bahamas?
            </SummaryTableData>
            <SummaryTableData>{transformBooleanStringToLabel(bahamasPremisesOwnership)}</SummaryTableData>
          </SummaryTableRow>
        </tbody>
      </SummaryTable>
      {bahamasPremisesOwnership === "true" && (
        <SummaryTable>
          <thead>
            <SummaryTableRow>
              <SummaryTableData>
                <p className="font-bold">Address Line 1</p>
              </SummaryTableData>
              <SummaryTableData><p className="font-bold">Address Line 2 </p></SummaryTableData>
              <SummaryTableData><p className="font-bold">Country</p></SummaryTableData>
            </SummaryTableRow>
          </thead>
          <tbody>
            {bahamasPremisesOwnership === "true" && premises && premises.map(p => (
              <SummaryTableRow key={`${p.addressLine1}-${p.country}`}>
                <SummaryTableData>
                  {p.addressLine1}
                </SummaryTableData>
                <SummaryTableData>{p.addressLine2}</SummaryTableData>
                <SummaryTableData>{p.country}</SummaryTableData>
              </SummaryTableRow>
            ))}
          </tbody>
        </SummaryTable>
      )}
    </div>
  )
}
