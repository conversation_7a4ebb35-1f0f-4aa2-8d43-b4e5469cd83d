import type { Dispatch, SetStateAction } from "react";
import type { Step } from "../types/step";
import { useEffect, useState } from "react";

function updateStepStatus(steps: Step[], currentStep: number): Step[] {
  return steps.map((step, index) => {
    if (index < currentStep - 1) {
      return { ...step, status: "complete" };
    } else if (index === currentStep - 1) {
      return { ...step, status: "current" };
    } else {
      return { ...step, status: "upcoming" };
    }
  });
}

export function useStepper(initialSteps: Step[], currentStepValue: number): {
  steps: Step[]
  currentStep: number
  setCurrentStep: Dispatch<SetStateAction<number>>
} {
  const [steps, setSteps] = useState(initialSteps);
  const [currentStep, setCurrentStep] = useState(currentStepValue);
  useEffect(() => {
    const updatedSteps = updateStepStatus(initialSteps, currentStep);
    setSteps(updatedSteps);
  }, [currentStep, setCurrentStep, initialSteps]);

  return { steps, currentStep, setCurrentStep };
}
