import { useLoaderData } from "@remix-run/react";
import { Fragment } from "react/jsx-runtime";
import type { TaxPayerIdentificationSchemaType } from "~/features/economic-substance-tbah/types/tax-payer-identification-schema";
import { Pages } from "~/features/economic-substance-tbah/utilities/form-pages";
import { transformBooleanStringToLabel } from "~/features/economic-substance-tbah/utilities/summary";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.submissions.$id.summary";
import { SummaryTable } from "./table/SummaryTable";
import { SummaryTableData } from "./table/SummaryTableData";
import { SummaryTableRow } from "./table/SummaryTableRow";

export function ParentEntity() {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { hasImmediateParentEntity, hasUltimateParentEntity, ultimateParentEntities, immediateParentEntities } = submissionData[Pages.TAX_PAYER_IDENTIFICATION] as TaxPayerIdentificationSchemaType

  return (
    <div>
      <h2 className="text-blue-700 font-bold mb-4">Parent Entity</h2>
      <SummaryTable>
        <tbody>
          {/* Ultimate Parent */}
          <SummaryTableRow>
            <SummaryTableData>
              Does entity have an ultimate parent entity?
            </SummaryTableData>
            <SummaryTableData>{transformBooleanStringToLabel(hasUltimateParentEntity)}</SummaryTableData>
          </SummaryTableRow>
          {hasUltimateParentEntity === "true"
          && ultimateParentEntities?.map((entity, index) => (
            <Fragment key={entity.name}>
              <SummaryTableRow className="border-b-0">
                <SummaryTableData>
                  <p className="font-semibold">
                    {`Ultimate Parent Name: ${entity.name}`}
                  </p>
                  <p>
                    {`Alternative  name: ${entity.alternativeName}`}
                  </p>
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow className="border-b-0 ">
                <SummaryTableData>
                  <p>
                    {`Jurisdiction of formation: ${entity.jurisdictionOfFormation}`}
                  </p>
                </SummaryTableData>
                <SummaryTableData>
                  <p>
                    {`Incorporation Number: ${entity.incorporationNumber}`}
                  </p>
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow className={index === ultimateParentEntities.length - 1 ? "" : "border-b-0"}>
                <SummaryTableData>
                  Taxpayer Identification Number(TIN#) or other Identification reference number:
                </SummaryTableData>
                <SummaryTableData>
                  {entity.taxpayerIdentificationNumber}
                </SummaryTableData>
              </SummaryTableRow>
            </Fragment>
          ))}
          {/* Immediate Parent */}
          <SummaryTableRow>
            <SummaryTableData>
              Does entity have an immediate parent entity?
            </SummaryTableData>
            <SummaryTableData>{transformBooleanStringToLabel(hasImmediateParentEntity)}</SummaryTableData>
          </SummaryTableRow>
          {hasImmediateParentEntity === "true"
          && immediateParentEntities?.map(entity => (
            <Fragment key={entity.name}>
              <SummaryTableRow className="border-b-0 ">
                <SummaryTableData>
                  <p className="font-semibold">
                    {`Immediate Parent Name: ${entity.name}`}
                  </p>
                  <p>
                    {`Alternative  name: ${entity.alternativeName}`}
                  </p>
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow className="border-b-0 ">
                <SummaryTableData>
                  <p>
                    {`Jurisdiction of formation: ${entity.jurisdictionOfFormation}`}
                  </p>
                </SummaryTableData>
                <SummaryTableData>
                  <p>
                    {`Incorporation Number: ${entity.incorporationNumber}`}
                  </p>
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow className="border-b-0">
                <SummaryTableData>
                  Taxpayer Identification Number(TIN#) or other Identification reference number:
                </SummaryTableData>
                <SummaryTableData>
                  {entity.taxpayerIdentificationNumber}
                </SummaryTableData>
              </SummaryTableRow>
            </Fragment>
          ))}
        </tbody>
      </SummaryTable>
    </div>
  )
}
