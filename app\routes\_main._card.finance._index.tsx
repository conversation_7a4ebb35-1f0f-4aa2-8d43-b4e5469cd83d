import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import type { JSX } from "react";
import { Outlet, redirect } from "@remix-run/react";
import { middleware } from "~/lib/middlewares.server";

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse | never> {
  await middleware(["auth"], request);

  return redirect("./companies");
}

export default function FinanceLayout(): JSX.Element {
  return <Outlet />;
}
