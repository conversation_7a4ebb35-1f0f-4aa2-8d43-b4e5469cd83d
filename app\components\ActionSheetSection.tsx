import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@netpro/design-system"
import { ChevronDown } from "lucide-react"
import { type FC, type PropsWithChildren, useState } from "react"
import { ActionSheetHeader } from "./ActionSheetHeader"

type Props = {
  title: string
  subTitle?: string
  collapsible?: boolean
  className?: string
} & PropsWithChildren

export const ActionSheetSection: FC<Props> = ({ title, subTitle, children, collapsible = false, className }) => {
  const [open, setOpen] = useState(true);

  if (!collapsible) {
    return (
      <div className={className}>
        <ActionSheetHeader subText={subTitle}>{title}</ActionSheetHeader>
        {children}
      </div>
    )
  }

  return (
    <Collapsible open={open} onOpenChange={setOpen} className={className}>
      <CollapsibleTrigger className="w-full">
        <div className="flex justify-between items-center">
          <ActionSheetHeader>{title}</ActionSheetHeader>
          <ChevronDown size={21} className={`transition-transform ${!open ? "rotate-180" : ""}`} />
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent>
        {children}
      </CollapsibleContent>
    </Collapsible>
  )
}
