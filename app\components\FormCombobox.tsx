import type { ComponentProps } from "react";
import { Combobox } from "@netpro/design-system";
import { FormFieldReset } from "~/components/FormFieldReset";
import { makeFormField } from "~/lib/makeFormField";

type Props = {
  options: { value: string, label: string }[]
  comboboxProps?: ComponentProps<typeof Combobox>
}

/**
 * FormCombobox is a higher-order component created with `makeForm<PERSON>ield` that integrates a combobox
 * with form state management. It provides a dropdown with search functionality and a reset option.
 *
 * @param {object} props - The props for the component.
 * @param {object} props.field - The form field object, containing the current value and an `onChange` handler.
 * @param {object} props.fieldState - The state of the field, including error information.
 * @param {Array<{ value: string, label: string }>} props.options - An array of options for the combobox. Each option includes a `value` and `label`.
 * @param {ComponentProps<typeof Combobox>} [props.comboboxProps] - Additional props to customize the underlying `Combobox` component.
 *
 * @returns {JSX.Element} A combobox with form integration, search functionality, and a reset button.
 *
 * @example
 * // Example usage with the Form and FilterRow components
 * import { z } from 'zod';
 * import { Form } from '~/components/Form';
 * import { useFilterForm } from '~/hooks/useFilterForm';
 * import { FormCombobox } from './FormCombobox';
 * import { FilterRow } from './FilterRow';
 *
 * const schema = z.object({
 *   country: z.string().optional(),
 * });
 *
 * const countryOptions = [
 *   { value: 'us', label: 'United States' },
 *   { value: 'ca', label: 'Canada' },
 *   { value: 'mx', label: 'Mexico' },
 * ];
 *
 * function FilterComponent() {
 *   const { formMethods } = useFilterForm(schema);
 *
 *   return (
 *     <Form formMethods={formMethods}>
 *       <FilterRow cols={5}>
 *         <FormCombobox
 *           name="country"
 *           label="Country"
 *           options={countryOptions}
 *           comboboxProps={{
 *             placeholder: "Select a country",
 *             searchText: "Search...",
 *             noResultsText: "No countries found.",
 *           }}
 *         />
 *       </FilterRow>
 *     </Form>
 *   );
 * }
 */
export const FormCombobox = makeFormField<Props>({ displayName: "FormCombobox", render: ({ field, fieldState, options, comboboxProps }) => {
  return (
    <div className="relative">
      <Combobox
        invalid={!!fieldState.error}
        items={options}
        onChange={field.onChange}
        value={field.value}
        defaultValue={field.value}
        {...comboboxProps}
      />
      {/*
          This Should ideally be part of the <Select.Trigger><Select.Icon>...
          markup but that is not a supported API atm
      */}
      {field.value && !comboboxProps?.disabled && (
        <FormFieldReset variant="select" onReset={() => field.onChange(comboboxProps?.multiple ? [] : "")} />
      )}
    </div>
  )
} })
