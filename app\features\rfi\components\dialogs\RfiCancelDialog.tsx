import type { ReactNode } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  Dialog<PERSON><PERSON><PERSON>,
  Spinner,
} from "@netpro/design-system";
import { useNavigation, useParams } from "@remix-run/react";
import { Form as RemixForm } from "@remix-run/react/dist/components";
import { useEffect, useState } from "react";
import { usePreserveQueryNavigate } from "~/lib/hooks/usePreserveQueryNavigate";

export function RfiCancelDialog({ routeName }: { routeName: string }): ReactNode {
  const navigate = usePreserveQueryNavigate();
  const navigation = useNavigation()
  const { id } = useParams()
  const isSubmitting = navigation.state === "submitting"
  const [isOpen, setIsOpen] = useState(false)
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    if (!open) {
      navigate(`/${routeName}/${id}`)
    }
  }

  useEffect(() => {
    setIsOpen(true)
  }, [])

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Cancel Request For Information
          </DialogTitle>
        </DialogHeader>
        <DialogDescription>
          Are you sure you want to cancel this request of information?
        </DialogDescription>
        <DialogFooter className="pt-4">
          <RemixForm
            method="post"
            noValidate
            className="flex gap-2"
          >
            <Button
              disabled={isSubmitting}
              variant="outline"
              type="button"
              onClick={() => handleOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              disabled={isSubmitting}
              type="submit"
              variant="destructive"
            >
              {isSubmitting ? <Spinner className="size-4 mx-0 text-white" /> : "Confirm"}
            </Button>
          </RemixForm>
        </DialogFooter>
      </DialogContent>
    </Dialog>

  )
}
