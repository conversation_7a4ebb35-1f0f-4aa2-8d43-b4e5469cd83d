import type { FormatOptions } from "date-fns";
// eslint-disable-next-line no-restricted-imports
import { format } from "date-fns";

/*
 *  This function currently formats dates using a predefined format ("dd-MM-yyyy").
 *  Although it may seem redundant now, it has been implemented as a forward-compatible
 *  feature to support potential future requirements where end-users may specify their
 *  own date formats.
 *
 *  By using this function, you can avoid excessive refactoring in the future if such
 *  customization becomes necessary.
 */

export type UseFormatDateOptions = {
  extend?: string
} & FormatOptions

export function useFormatDate<DateType extends Date>(): (date: DateType | number | string, globalOptions?: UseFormatDateOptions) => string {
  const internalFormat = (date: DateType | number | string, localOptions?: UseFormatDateOptions): string => {
    const { extend, ...options } = localOptions || {}

    return format(date, `dd-MMM-yyyy${extend ?? ""}`, options)
  }

  return internalFormat;
}
