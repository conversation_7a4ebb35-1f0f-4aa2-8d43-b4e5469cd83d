import type { ReactNode } from "react";
import { But<PERSON> } from "@netpro/design-system";
import { Outlet, useLoaderData, useSearchParams } from "@remix-run/react";
import { Filter } from "lucide-react";
import { PageErrorBoundary } from "~/components/errors/PageErrorBoundary";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormSearch } from "~/components/FormSearch";
import { Pagination } from "~/components/ui/filters/Pagination";
import { PageMessage } from "~/components/ui/PageMessage";
import { FinancialReportListItem } from "~/features/financial-reports/FinancialReportListItem";
import { searchSchema } from "~/features/financial-reports/schemas/search-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { managementGetReportsByType } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Financial Reports",
    to: "/basic-financial-report/financial-reports",
  },
  title: "Financial Reports",
};

export const loader = makeEnhancedLoader(async ({ request, json, queryString }) => {
  await middleware(["auth"], request);
  const schemaData = searchSchema.safeParse(queryString).data
  const { pageNumber, pageSize } = await getFilterParams({ request });
  const financialReportResponse = await managementGetReportsByType({ headers: await authHeaders(request), query: {
    PageNumber: pageNumber,
    PageSize: pageSize,
    ReportTypes: ["BasicFinancialReport"],
    SearchTerm: schemaData?.search,
  } });

  if (!financialReportResponse.data) {
    throw new Response("failed fetch financial reports data")
  }

  return json({ financialReports: financialReportResponse.data });
}, { authorize: ["bfr.panama.invoices.export"] })

export default function BasicFinancialReportFinancialReports(): ReactNode {
  const [searchParams] = useSearchParams();
  const { financialReports } = useLoaderData<typeof loader>();
  const searchTerm = searchParams.get("search") ?? false;
  const { formMethods } = useFilterForm(searchSchema)
  let message = "Unable to load Financial reports data. Please try again later.";
  if (searchTerm) {
    message = `No financial reports found for search term "${searchTerm}".`;
  }

  return (
    <div className="p-2 w-full h-auto flex flex-col">
      <div className="flex flex-col flex-1">
        <Form formMethods={formMethods}>
          <FilterRow>
            <div className="col-span-full flex flex-row items-center gap-2">
              <FormSearch
                name="search"
                formItemProps={{ className: "w-full" }}
                inputProps={{ placeholder: "Search financial reports" }}
              />
              <Button size="sm" className="gap-1.5" type="submit">
                <Filter size={14} />
                Apply Filter(s)
              </Button>
            </div>
          </FilterRow>
        </Form>
        <div className="flex-1 mt-4 overflow-y-auto">
          {financialReports?.data?.length === 0
            ? (
                <PageMessage
                  title="No financial reports found"
                  subtitle={message}
                />
              )
            : financialReports?.data?.map(report => (
              <FinancialReportListItem
                key={report.id}
                report={report}
              />
            ))}
        </div>
        {financialReports.data && financialReports.data.length > 0 && (
          <Pagination totalItems={financialReports?.totalItemCount || 0} />
        )}
      </div>

      <Outlet context={{ financialReports }} />
    </div>
  );
}

export const ErrorBoundary = PageErrorBoundary;
