import { z } from "zod";
import { fileSchema } from "./file-schema";

export function supportingDetailsSchema(relevantActivityNone: "true" | "false" | undefined) {
  return z.object({
    additionalComments: z.string().max(255, "Additional comments cannot exceed 255 characters").optional(),
    files_supportingAttachments: fileSchema.optional(),
  })
    .refine((data) => {
      if (relevantActivityNone === "true") {
        return data.additionalComments && data.additionalComments.trim() !== "";
      }

      return true;
    }, {
      message: "Provide an explanation for your statement as you have selected 'none' for your relevant activities",
      path: ["additionalComments"],
    });
}

export type SupportingDetailsSchemaType = z.infer<ReturnType<typeof supportingDetailsSchema>>
