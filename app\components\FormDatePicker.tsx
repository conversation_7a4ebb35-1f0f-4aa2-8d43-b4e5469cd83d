import type { ComponentProps } from "react";
import { DatePicker } from "@netpro/design-system";
import { FormFieldReset } from "~/components/FormFieldReset";
import { makeFormField } from "~/lib/makeFormField";

/**
 * FormDatePicker is a higher-order component created with `makeFormField` that integrates a date picker
 * with form state management. It allows users to select a date and provides an option to reset the selected value.
 *
 * @param {object} props - The props for the component.
 * @param {object} props.field - The form field object, containing the current value and an `onChange` handler.
 * @param {object} props.fieldState - The state of the field, including error information.
 * @param {ComponentProps<typeof DatePicker>} [props.datePickerProps] - Additional props to customize the underlying `DatePicker` component.
 *
 * @returns {JSX.Element} A date picker with form integration and a reset button.
 *
 * @example
 * // Example usage with useFilterForm hook, Form, and FilterRow
 * import { z } from 'zod';
 * import { Form } from '~/components/Form';
 * import { useFilterForm } from '~/hooks/useFilterForm';
 * import { FormDatePicker } from './FormDatePicker';
 * import { FilterRow } from './FilterRow';
 *
 * const schema = z.object({
 *   submittedAfter: z.string().optional(),
 * });
 *
 * function FilterComponent({ columns }) {
 *   const { formMethods } = useFilterForm(schema);
 *
 *   return (
 *     <Form formMethods={formMethods}>
 *       <FilterRow cols={5}>
 *         <FormDatePicker name="submittedAfter" label="Submitted After" />
 *       </FilterRow>
 *     </Form>
 *   );
 * }
 */
type DatePickerProps = Omit<ComponentProps<typeof DatePicker>, "onChange" | "date">

export const FormDatePicker = makeFormField<{ datePickerProps?: DatePickerProps }>({ displayName: "FormDatePicker", render: ({ field, fieldState, datePickerProps }) => {
  return (
    <div className="relative">
      <DatePicker
        date={field.value}
        onChange={field.onChange}
        invalid={!!fieldState.error}
        {...datePickerProps}
      />
      {field.value && !datePickerProps?.disabled
      && (
        <FormFieldReset onReset={() => field.onChange("")} />
      )}
    </div>
  )
} })
