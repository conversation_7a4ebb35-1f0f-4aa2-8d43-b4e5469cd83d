export function formatYesNoBoolean(value: "true" | "false" | boolean | undefined): string | undefined {
  if (typeof value === "boolean") {
    return value ? "Yes " : "No";
  }

  if (typeof value === "string") {
    return value === "true" ? "Yes" : "No";
  }

  return undefined;
}

export function formatDate(date: Date | string): string {
  return new Date(date).toLocaleDateString("en-GB", {
    month: "2-digit",
    day: "2-digit",
    year: "numeric",
  }).replace(/\//g, "-");
}

export function formatDateToUTC(date: Date): Date {
  return new Date(Date.UTC(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    0,
    0,
    0,
    0,
  ));
}
