import type { LoaderFunctionArgs } from "@remix-run/node";
import type { JSX } from "react";
import { Outlet } from "@remix-run/react";
import { middleware } from "~/lib/middlewares.server";

export async function loader({ request }: LoaderFunctionArgs): Promise<null | never> {
  await middleware(["auth"], request);

  return null;
}

export default function ReportsLayout(): JSX.Element {
  return <Outlet />;
}
