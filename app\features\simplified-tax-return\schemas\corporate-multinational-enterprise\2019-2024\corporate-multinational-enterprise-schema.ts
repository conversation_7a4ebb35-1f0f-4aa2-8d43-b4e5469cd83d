import { z } from "zod";
import { stringBoolean } from "~/lib/utilities/zod-validators";

export const corporateMultinationalEnterpriseSchema = z.object({
  isPartOfMNEGroup: stringBoolean(),
  requiresCbCReport: stringBoolean().optional(),
}).refine(data => !(data.isPartOfMNEGroup === "true" && !data.requiresCbCReport), {
  message: "Required",
  path: ["requiresCbCReport"],
})

export type CorporateMultinationalEnterpriseType = z.infer<typeof corporateMultinationalEnterpriseSchema>;
