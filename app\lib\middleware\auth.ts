import { redirect } from "@remix-run/react";
import { commitSession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import type { MiddlewareProps, MiddlewareResponse } from "~/lib/middlewares.server";

type AuthenticatedSessionData = {
  userId: string
  accessToken: string
  validSession: true
};

export default async function auth({ request }: MiddlewareProps): MiddlewareResponse<never | AuthenticatedSessionData> {
  const { userId, accessToken } = await getSessionData(request);
  if (!userId || !accessToken) {
    throw redirect("/login");
  }

  // Refresh Access Token if it's expired
  const session = await getSession(request.headers.get("Cookie"));
  const accessTokenExpiresOn = session.get("accessTokenExpiresOn");
  const url = new URL(request.url);
  if (accessTokenExpiresOn && new Date(accessTokenExpiresOn) < new Date()) {
    session.flash("redirect", url.pathname);
    const objectId = session.get("objectId") as string;

    console.error(`Refreshing access token for user with object ID ${objectId}.`);
    throw redirect("/auth/application", { headers: { "Set-Cookie": await commitSession(session) } });
  }

  return { userId, accessToken, validSession: true }
}
