import type { LinksFunction } from "@remix-run/node";
import type { JSX } from "react";
import { But<PERSON> } from "@netpro/design-system";
import { Outlet } from "@remix-run/react";
import { Download } from "lucide-react";
import { defaultLinks } from "~/lib/config";
import printStyles from "~/print.css?url";

export const links: LinksFunction = () => [
  ...defaultLinks,
  { rel: "stylesheet", href: printStyles, media: "print" },
];

export default function PDFLayout(): JSX.Element {
  const handlePrint = (): void => {
    window.print();
  };

  return (
    <div className="pdf-layout relative bg-gray-900 min-h-full">
      <Button
        variant="default"
        onClick={handlePrint}
        className="print:hidden fixed top-4 right-4 flex items-center gap-2 "
      >
        <Download size={16} />
        {" "}
        Download
      </Button>
      <Outlet />
    </div>
  );
}
