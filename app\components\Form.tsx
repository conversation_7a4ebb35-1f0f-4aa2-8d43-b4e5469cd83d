import type { FormProps as RemixFormProps } from "@remix-run/react";
import type { FC, PropsWithChildren } from "react";
import { Form as NetProForm } from "@netpro/design-system";
import { Form as RemixForm } from "@remix-run/react";
import clsx from "clsx";
import { RemixFormProvider } from "remix-hook-form";

// should be UseRemixFormReturn<any> but UseRemixFormReturn doesn't currently accept a generic
type Props = {
  formMethods: any
  remixFormProps?: RemixFormProps
} & PropsWithChildren

/**
 * Form is a composed form component that integrates Remix forms, the NetPro design system,
 * and form state management via `remix-hook-form`. It provides a structured way to manage
 * form elements and submission logic.
 *
 * @template Props - The props type for the component.
 *
 * @param {object} props - The props for the component.
 * @param {any} props.formMethods - The form methods returned by `useRemixForm` for managing form state and validation.
 * @param {RemixFormProps} [props.remixFormProps] - Additional props to configure the underlying Remix `<Form />` component. Defaults `method` to "GET" if not provided.
 * @param {ReactNode} props.children - The form elements to be rendered inside the form.
 *
 * @returns {JSX.Element} A composed form with integrated state management, validation, and styling.
 *
 * @example
 * // Example usage with formMethods
 * import { Form } from './Form';
 * import { useRemixForm } from 'remix-hook-form';
 *
 * function MyFormComponent() {
 *   const formMethods = useRemixForm();
 *
 *   return (
 *     <Form formMethods={formMethods} remixFormProps={{ method: 'POST' }}>
 *
 *     </Form>
 *   );
 * }
 */
export const Form: FC<Props> = ({ children, formMethods, remixFormProps }) => {
  // Set the default value of remixFormProps.method to GET if its not provided
  if (!remixFormProps) {
    remixFormProps = { method: "GET" }
  } else if (!remixFormProps?.method) {
    remixFormProps.method = "GET"
  }

  return (
    <RemixFormProvider {...formMethods}>
      <NetProForm {...formMethods as any}>
        <RemixForm
          onSubmit={formMethods.handleSubmit}
          noValidate
          className={clsx(
            "flex flex-col gap-y-2 mb-4 w-full",
            remixFormProps?.className,
          )}
          {...remixFormProps}
        >
          {children}
        </RemixForm>
      </NetProForm>
    </RemixFormProvider>
  )
}
