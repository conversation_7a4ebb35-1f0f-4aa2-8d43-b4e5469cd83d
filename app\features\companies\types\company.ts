export type Company = {
  id: string
  name: string
  incorporationNumber: string
  isActive: boolean
  legacyCode: string | null
  incorporationDate: string
  referralOffice: string | null
  onboardingStatus: number
  masterClientId: string
  masterClientCode: string
  jurisdictionId: string
  jurisdictionName: string
  code: string
};

export type CompaniesResponse = {
  pageNumber: number
  pageCount: number
  pageSize: number
  totalItemCount: number
  hasPrevious: boolean
  hasNext: boolean
  data: Company[]
};

export type FilingDates = {
  applicationDate: string
  approvedStartDate: string
  approvedEndDate: string
}

export type Modules = {
  simplifiedTaxReturn: boolean
  boDirectorsModule: boolean
}

export type CompanyModule = {
  id: string
  name: string
  key: string
  isActive: boolean
  isEnabled: boolean
  isApproved: boolean
  jurisdictionIsEnabled: boolean
};

export type CompanyModulesResponse = {
  modules: CompanyModule[]
};

export type GetCompanyModulesParams = {
  companyId: string
};

export type CompanySettingsKey = "fees" | "submissions";

export type GetCompanySettingsParams = {
  companyId: string
  key: CompanySettingsKey
};

export type UpdateCompanySettingsParams = {
  companyId: string
  key: CompanySettingsKey
  data: Record<string, unknown>
};

export type CompanySettingsResponse = Record<string, unknown>;

export type CompanyWithModules = Company & ModulesEnabled & FilingDates & {
  simplifiedTaxReturnApproved: boolean
  boDirectorsModuleApproved: boolean
}

export type ModulesEnabled = {
  boDirectorsModuleEnabled: boolean
  simplifiedTaxReturnEnabled: boolean
}

export type UpdateCompanyModulesRequest = {
  modules: Array<{
    id: string
    isEnabled: boolean
    isApproved: boolean
  }>
}

export type UpdateCompanyModulesResponse = {
  modules: Array<{
    id: string
    name: string
    key: string
    isActive: boolean
    isEnabled: boolean
    isApproved: boolean
    jurisdictionIsEnabled: boolean
  }>
}
