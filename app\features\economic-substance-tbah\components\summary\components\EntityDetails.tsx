import { useLoaderData } from "@remix-run/react";
import type { FinancialPeriodSchemaType } from "~/features/economic-substance-tbah/types/financial-period-schema";
import { dayMonthYearFormat, formatDateWithFallback } from "~/features/economic-substance-tbah/utilities/date";
import { Pages } from "~/features/economic-substance-tbah/utilities/form-pages";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.submissions.$id.summary";

export function EntityDetails() {
  const { submissionData, entityDetails } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { legalEntityName, companyIdentityCode, masterClientCode, status, submittedAt } = entityDetails
  const submissionDate = formatDateWithFallback(submittedAt, dayMonthYearFormat)
  const { startDate, endDate } = submissionData[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType
  const financialPeriodStart = formatDateWithFallback(startDate, dayMonthYearFormat)
  const financialPeriodEnd = formatDateWithFallback(endDate, dayMonthYearFormat)

  return (
    <div>
      <h2 className="text-blue-700 font-bold mb-4">Entity Details</h2>
      <div className="border-2 border-blue-200 p-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-3">
            <div>
              <span>Entity Name: </span>
              <span className="font-semibold">
                {legalEntityName}
              </span>
            </div>
            <div>
              <span>VP Code: </span>
              <span className="font-semibold">
                {companyIdentityCode}
              </span>
            </div>
            <div>
              <span>Master Client Code: </span>
              <span className="font-semibold">
                {masterClientCode}
              </span>
            </div>
            <div>
              <span>Registered Agent: </span>
              <div className="mt-1">
                <span className="font-semibold">
                  Trident Trust Company (Bahamas) Limited
                </span>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div>
              <span>Reporting period:</span>
              <div className="mt-3 font-semibold">
                {`${financialPeriodStart} - ${financialPeriodEnd}`}
              </div>
            </div>
            <div className="mt-4">
              <span>Submitted Date: </span>
              <span className="font-semibold">
                {submissionDate}
              </span>
            </div>
            <div className="mt-4">
              <span>Status: </span>
              <span className="font-bold">{status}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

  )
}
