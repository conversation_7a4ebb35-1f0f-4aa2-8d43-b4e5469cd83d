import { z } from "zod";
import { stringBoolean } from "~/lib/utilities/zod-validators";

export const corporateAddressSchema = z.object({
  recordsKeptAtRegisteredOffice: stringBoolean(),
  nevisAddress: z.string().max(5000, {
    message: "Address must be less than 5000 characters",
  }).optional(),
  recordsPlaceAddress: z.string().max(5000, {
    message: "Address must be less than 5000 characters",
  }).optional(),
})
  .refine(data => !(data.recordsKeptAtRegisteredOffice === "true" && !data.nevisAddress?.length), {
    message: "Address is required",
    path: ["nevisAddress"],
  })
  .refine(data => !(data.recordsKeptAtRegisteredOffice === "false" && !data.recordsPlaceAddress?.length), {
    message: "Address required",
    path: ["recordsPlaceAddress"],
  })

export type CorporateAddressType = z.infer<typeof corporateAddressSchema>;
