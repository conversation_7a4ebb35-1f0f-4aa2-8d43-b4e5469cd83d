import type { LoaderFunctionArgs } from "@remix-run/node";
import type { JSX } from "react";
import { middleware } from "~/lib/middlewares.server";

export const handle = {
  breadcrumb: {
    label: "Export Invoices",
    to: "/finance/export-invoices",
  },
  title: "Export Invoices",
}

export async function loader({ request }: LoaderFunctionArgs): Promise<null | never> {
  await middleware(["auth"], request);

  return null;
}

export default function ExportInvoices(): JSX.Element {
  return <div>ExportInvoices</div>;
}
