// This file is auto-generated by @hey-api/openapi-ts

import { createClient, createConfig, formDataBodySerializer, type Options } from '@hey-api/client-fetch';
import type { AcceptTermsConditionsData, AcceptTermsConditionsError, AcceptTermsConditionsResponse, AddActivityLogData, AddActivityLogError, AddActivityLogResponse, AddUserToMasterClientData, AddUserToMasterClientError, AddUserToMasterClientResponse, ApproveCompanyData, ApproveCompanyError, ApproveCompanyResponse, BlockUnblockUserData, BlockUnblockUserError, BlockUnblockUserResponse, ClientBeneficialOwnerUpdateSimulationData, ClientBeneficialOwnerUpdateSimulationError, ClientBeneficialOwnerUpdateSimulationResponse, ClientCancelPaymentData, ClientCancelPaymentError, ClientCancelPaymentResponse, ClientConfirmBeneficialOwnerData, ClientConfirmBeneficialOwnerError, ClientConfirmBeneficialOwnerResponse, ClientConfirmDirectorData, ClientConfirmDirectorError, ClientConfirmDirectorResponse, ClientConfirmShareholderData, ClientConfirmShareholderError, ClientConfirmShareholderResponse, ClientCreatePaymentData, ClientCreatePaymentError, ClientCreatePaymentResponse, ClientCreatePaymentTransactionData, ClientCreatePaymentTransactionError, ClientCreatePaymentTransactionResponse, ClientCreateSubmissionData, ClientCreateSubmissionError, ClientCreateSubmissionResponse, ClientDeleteSubmissionData, ClientDeleteSubmissionError, ClientDeleteSubmissionResponse, ClientDirecctorUpdateSimulationData, ClientDirecctorUpdateSimulationError, ClientDirecctorUpdateSimulationResponse, ClientGetAvailableSubmissionYearsData, ClientGetAvailableSubmissionYearsError, ClientGetAvailableSubmissionYearsResponse, ClientGetBeneficialOwnerData, ClientGetBeneficialOwnerError, ClientGetBeneficialOwnerForComparisonData, ClientGetBeneficialOwnerForComparisonError, ClientGetBeneficialOwnerForComparisonResponse, ClientGetBeneficialOwnerResponse, ClientGetCompanyBeneficialOwnersData, ClientGetCompanyBeneficialOwnersError, ClientGetCompanyBeneficialOwnersResponse, ClientGetCompanyDirectorsData, ClientGetCompanyDirectorsError, ClientGetCompanyDirectorsResponse, ClientGetCompanyModulesData, ClientGetCompanyModulesError, ClientGetCompanyModulesResponse, ClientGetCompanyModuleSubmissionsData, ClientGetCompanyModuleSubmissionsError, ClientGetCompanyModuleSubmissionsResponse, ClientGetDirectorData, ClientGetDirectorError, ClientGetDirectorForComparisonData, ClientGetDirectorForComparisonError, ClientGetDirectorForComparisonResponse, ClientGetDirectorResponse, ClientGetInvoiceByIdData, ClientGetInvoiceByIdError, ClientGetInvoiceByIdResponse, ClientGetInvoicesData, ClientGetInvoicesError, ClientGetInvoicesResponse, ClientGetMasterClientCompaniesData, ClientGetMasterClientCompaniesError, ClientGetMasterClientCompaniesResponse, ClientGetMasterClientsData, ClientGetMasterClientsError, ClientGetMasterClientsResponse, ClientGetMasterClientSubmissionsData, ClientGetMasterClientSubmissionsError, ClientGetMasterClientSubmissionsResponse, ClientGetPaymentData, ClientGetPaymentError, ClientGetPaymentResponse, ClientGetPaymentsData, ClientGetPaymentsError, ClientGetPaymentsResponse, ClientGetPermissionsData, ClientGetPermissionsError, ClientGetPermissionsResponse, ClientGetShareholderData, ClientGetShareholderError, ClientGetShareholderResponse, ClientGetSubmissionData, ClientGetSubmissionError, ClientGetSubmissionResponse, ClientGetSubmissionRfiDetailsData, ClientGetSubmissionRfiDetailsError, ClientGetSubmissionRfiDetailsResponse, ClientPutSubmissionDataSetData, ClientPutSubmissionDataSetError, ClientPutSubmissionDataSetResponse, ClientRequestAssistanceData, ClientRequestAssistanceError, ClientRequestAssistanceResponse, ClientRequestBeneficialOwnerUpdateData, ClientRequestBeneficialOwnerUpdateError, ClientRequestBeneficialOwnerUpdateResponse, ClientRequestDirectorUpdateData, ClientRequestDirectorUpdateError, ClientRequestDirectorUpdateResponse, ClientRequestShareholderUpdateData, ClientRequestShareholderUpdateError, ClientRequestShareholderUpdateResponse, ClientRfiCcompleteData, ClientRfiCcompleteError, ClientRfiCcompleteResponse, ClientRfiDocumentCreateData, ClientRfiDocumentCreateError, ClientRfiDocumentCreateResponse, ClientSubmitPaymentTransactionData, ClientSubmitPaymentTransactionError, ClientSubmitPaymentTransactionResponse, ClientSubmitSubmissionData, ClientSubmitSubmissionError, ClientSubmitSubmissionResponse, ClientUpdateSubmissionInformationData, ClientUpdateSubmissionInformationError, ClientUpdateSubmissionInformationResponse, ConfirmMfaResetData, ConfirmMfaResetError, ConfirmMfaResetResponse, CreateCurrencyData, CreateCurrencyError, CreateCurrencyResponse, CreateDocumentData, CreateDocumentError, CreateDocumentResponse, CreateFormTemplateVersionData, CreateFormTemplateVersionError, CreateFormTemplateVersionResponse, CreateMessageReadStatusData, CreateMessageReadStatusError, CreateMessageReadStatusResponse, CreateOrUpdateTaxRateData, CreateOrUpdateTaxRateError, CreateOrUpdateTaxRateResponse, CreateUserData, CreateUserError, CreateUserResponse, DeclineCompanyData, DeclineCompanyError, DeclineCompanyResponse, DeleteCurrencyData, DeleteCurrencyError, DeleteCurrencyResponse, DeleteInboxReadStatusData, DeleteInboxReadStatusError, DeleteInboxReadStatusResponse, DeleteTaxRateData, DeleteTaxRateError, DeleteTaxRateResponse, GetActivityLogData, GetActivityLogError, GetActivityLogResponse, GetAllCurrenciesData, GetAllCurrenciesError, GetAllCurrenciesResponse, GetAllPermissionsData, GetAllPermissionsError, GetAllPermissionsResponse, GetApiV1CommonDocumentsByDocumentIdData, GetApiV1CommonDocumentsByDocumentIdError, GetApiV1CommonDocumentsByDocumentIdResponse, GetApiV1CommonDocumentsData, GetApiV1CommonDocumentsError, GetApiV1CommonDocumentsResponse, GetApiV1ToolsMfaMfaInfoAuthenticatorData, GetApiV1ToolsMfaMfaInfoAuthenticatorError, GetApiV1ToolsMfaMfaInfoAuthenticatorResponse, GetApiV1ToolsMfaMfaInfoEmailData, GetApiV1ToolsMfaMfaInfoEmailError, GetApiV1ToolsMfaMfaInfoEmailResponse, GetApiV1ToolsMfaMfaVerificationData, GetApiV1ToolsMfaMfaVerificationError, GetApiV1ToolsMfaMfaVerificationResponse, GetAuditData, GetAuditError, GetAuditResponse, GetCompaniesData, GetCompaniesError, GetCompaniesResponse, GetCompanyAnnualFeesData, GetCompanyAnnualFeesError, GetCompanyAnnualFeesResponse, GetCompanyAnnualFeeStatusByCompanyData, GetCompanyAnnualFeeStatusByCompanyError, GetCompanyAnnualFeeStatusByCompanyResponse, GetCompanyByIdData, GetCompanyByIdError, GetCompanyByIdResponse, GetCompanyModulesData, GetCompanyModulesError, GetCompanyModulesResponse, GetCompanySettingsData, GetCompanySettingsError, GetCompanySettingsResponse, GetFormTemplateData, GetFormTemplateError, GetFormTemplateResponse, GetFormTemplatesData, GetFormTemplatesError, GetFormTemplatesResponse, GetInboxInfoData, GetInboxInfoError, GetInboxInfoResponse, GetInboxMessageData, GetInboxMessageError, GetInboxMessageResponse, GetInboxMessagesData, GetInboxMessagesError, GetInboxMessagesResponse, GetJurisdictionFormTemplatesData, GetJurisdictionFormTemplatesError, GetJurisdictionFormTemplatesResponse, GetJurisdictionModulesData, GetJurisdictionModulesError, GetJurisdictionModulesResponse, GetJurisdictionsData, GetJurisdictionsError, GetJurisdictionSettingsData, GetJurisdictionSettingsError, GetJurisdictionSettingsResponse, GetJurisdictionsResponse, GetJurisdictionTaxRatesData, GetJurisdictionTaxRatesError, GetJurisdictionTaxRatesResponse, GetMasterClientCompaniesData, GetMasterClientCompaniesError, GetMasterClientCompaniesResponse, GetMasterClientData, GetMasterClientError, GetMasterClientResponse, GetMasterClientsData, GetMasterClientsError, GetMasterClientSettingsData, GetMasterClientSettingsError, GetMasterClientSettingsResponse, GetMasterClientsResponse, GetMasterClientUsersData, GetMasterClientUsersError, GetMasterClientUsersResponse, GetMfaMethodData, GetMfaMethodError, GetMfaMethodResponse, GetMigrationLogWorkbookData, GetMigrationLogWorkbookError, GetMigrationLogWorkbookResponse, GetMigrationStatusData, GetMigrationStatusError, GetMigrationStatusResponse, GetModulesData, GetModulesError, GetModulesResponse, GetPermissionsData, GetPermissionsError, GetPermissionsResponse, GetTaxRatesByJurisdictionData, GetTaxRatesByJurisdictionError, GetTaxRatesByJurisdictionResponse, GetTermsConditionsStatusData, GetTermsConditionsStatusError, GetTermsConditionsStatusResponse, ListActivityLogsData, ListActivityLogsError, ListActivityLogsResponse, ListAuditsData, ListAuditsError, ListAuditsResponse, ManagementAnnouncementCreateData, ManagementAnnouncementCreateError, ManagementAnnouncementCreateResponse, ManagementAnnouncementGetByIdData, ManagementAnnouncementGetByIdError, ManagementAnnouncementGetByIdResponse, ManagementAnnouncementUpdateData, ManagementAnnouncementUpdateError, ManagementAnnouncementUpdateResponse, ManagementBahamasGenerateSubmissionsReportData, ManagementBahamasGenerateSubmissionsReportError, ManagementBahamasGenerateSubmissionsReportResponse, ManagementBahamasListSubmissionsByModuleData, ManagementBahamasListSubmissionsByModuleError, ManagementBahamasListSubmissionsByModuleResponse, ManagementCancelRfiData, ManagementCancelRfiError, ManagementCancelRfiResponse, ManagementCreateAnnouncementDocumentData, ManagementCreateAnnouncementDocumentError, ManagementCreateAnnouncementDocumentResponse, ManagementDeleteAnnouncementData, ManagementDeleteAnnouncementDocumentData, ManagementDeleteAnnouncementDocumentError, ManagementDeleteAnnouncementDocumentResponse, ManagementDeleteAnnouncementError, ManagementDeleteAnnouncementResponse, ManagementDownloadBoDirListData, ManagementDownloadBoDirListError, ManagementDownloadBoDirListResponse, ManagementDownloadReportData, ManagementDownloadReportError, ManagementDownloadReportResponse, ManagementDownloadSubmissionDocumentsData, ManagementDownloadSubmissionDocumentsError, ManagementDownloadSubmissionDocumentsResponse, ManagementDownloadSubmissionsDocumentsData, ManagementDownloadSubmissionsDocumentsError, ManagementDownloadSubmissionsDocumentsResponse, ManagementExportSubmissionData, ManagementExportSubmissionDataData, ManagementExportSubmissionDataError, ManagementExportSubmissionDataResponse, ManagementExportSubmissionError, ManagementExportSubmissionResponse, ManagementGenerateNevisSubmissionsReportData, ManagementGenerateNevisSubmissionsReportError, ManagementGenerateNevisSubmissionsReportResponse, ManagementGenerateSubmissionsReportData, ManagementGenerateSubmissionsReportError, ManagementGenerateSubmissionsReportResponse, ManagementGetAllSubmissionYearsData, ManagementGetAllSubmissionYearsError, ManagementGetAllSubmissionYearsResponse, ManagementGetAppVersionData, ManagementGetAppVersionError, ManagementGetAppVersionResponse, ManagementGetBeneficialOwnerData, ManagementGetBeneficialOwnerError, ManagementGetBeneficialOwnerForComparisonData, ManagementGetBeneficialOwnerForComparisonError, ManagementGetBeneficialOwnerForComparisonResponse, ManagementGetBeneficialOwnerResponse, ManagementGetDirectorData, ManagementGetDirectorError, ManagementGetDirectorForComparisonData, ManagementGetDirectorForComparisonError, ManagementGetDirectorForComparisonResponse, ManagementGetDirectorResponse, ManagementGetInvoiceByIdData, ManagementGetInvoiceByIdError, ManagementGetInvoiceByIdResponse, ManagementGetPaidStatusByCompanyAndYearData, ManagementGetPaidStatusByCompanyAndYearError, ManagementGetPaidStatusByCompanyAndYearResponse, ManagementGetReportsByTypeData, ManagementGetReportsByTypeError, ManagementGetReportsByTypeResponse, ManagementGetSubmissionData, ManagementGetSubmissionError, ManagementGetSubmissionResponse, ManagementGetSubmissionRfiDetailsData, ManagementGetSubmissionRfiDetailsError, ManagementGetSubmissionRfiDetailsResponse, ManagementGetUserData, ManagementGetUserError, ManagementGetUserResponse, ManagementGetUsersData, ManagementGetUsersError, ManagementGetUsersResponse, ManagementGetViewPointSyncStatusData, ManagementGetViewPointSyncStatusError, ManagementGetViewPointSyncStatusResponse, ManagementListAnnouncementsData, ManagementListAnnouncementsError, ManagementListAnnouncementsResponse, ManagementListBoDirsData, ManagementListBoDirsError, ManagementListBoDirsResponse, ManagementListRfiSubmissionsData, ManagementListRfiSubmissionsError, ManagementListRfiSubmissionsResponse, ManagementListSubmissionsData, ManagementListSubmissionsError, ManagementListSubmissionsResponse, ManagementMarkAsPaidByCompanyAndYearData, ManagementMarkAsPaidByCompanyAndYearError, ManagementMarkAsPaidByCompanyAndYearResponse, ManagementMarkAsPaidData, ManagementMarkAsPaidError, ManagementMarkAsPaidResponse, ManagementPanamaListSubmissionsByModuleData, ManagementPanamaListSubmissionsByModuleError, ManagementPanamaListSubmissionsByModuleResponse, ManagementReopenSubmissionData, ManagementReopenSubmissionError, ManagementReopenSubmissionResponse, ManagementRfiCreateData, ManagementRfiCreateError, ManagementRfiCreateResponse, ManagementRfiDocumentCreateData, ManagementRfiDocumentCreateError, ManagementRfiDocumentCreateResponse, ManagementUpdateSubmissionInformationData, ManagementUpdateSubmissionInformationError, ManagementUpdateSubmissionInformationResponse, PatchMfaMethodData, PatchMfaMethodError, PatchMfaMethodResponse, PostApiV1ExternalidOnAttributeCollectionStartData, PostApiV1ExternalidOnAttributeCollectionStartError, PostApiV1ExternalidOnAttributeCollectionStartResponse, PostApiV1ExternalidOnAttributeCollectionSubmitData, PostApiV1ExternalidOnAttributeCollectionSubmitError, PostApiV1ExternalidOnAttributeCollectionSubmitResponse, PostApiV1ImportsMasterclientsData, PostMigrationCleanupData, PostMigrationCleanupError, PostMigrationCleanupResponse, PostOnattributecollectionstartData, PostOnattributecollectionstartError, PostOnattributecollectionstartResponse, PostOnattributecollectionsubmitData, PostOnattributecollectionsubmitError, PostOnattributecollectionsubmitResponse, PutMfaMethodData, PutMfaMethodError, PutMfaMethodResponse, RemoveUserFromMasterClientData, RemoveUserFromMasterClientError, RemoveUserFromMasterClientResponse, RequestMfaResetData, RequestMfaResetError, RequestMfaResetResponse, ResetMfaMethodData, ResetMfaMethodError, ResetMfaMethodResponse, SecuritySetUserSignedInData, SecuritySetUserSignedInError, SecuritySetUserSignedInResponse, SecuritySetUserSignedOutData, SecuritySetUserSignedOutError, SecuritySetUserSignedOutResponse, SendCompanyEmailMessageData, SendCompanyEmailMessageError, SendCompanyEmailMessageResponse, SendEmailMessageData, SendEmailMessageError, SendEmailMessageResponse, SendMfaEmailData, SendMfaEmailError, SendMfaEmailResponse, SendUserInvitationData, SendUserInvitationError, SendUserInvitationResponse, SendUserInvitationsData, SendUserInvitationsError, SendUserInvitationsResponse, SetCompanyAnnualFeeStatusByCompanyData, SetCompanyAnnualFeeStatusByCompanyError, SetCompanyAnnualFeeStatusByCompanyResponse, SetCompanyAnnualFeeStatusData, SetCompanyAnnualFeeStatusError, SetCompanyAnnualFeeStatusResponse, SetCompanyModulesData, SetCompanyModulesError, SetCompanyModulesResponse, SetCompanySettingsByKeyData, SetCompanySettingsByKeyError, SetCompanySettingsByKeyResponse, SetCompanySettingsData, SetCompanySettingsError, SetCompanySettingsResponse, SetJurisdictionModulesData, SetJurisdictionModulesError, SetJurisdictionModulesResponse, SetJurisdictionSettingsByKeyData, SetJurisdictionSettingsByKeyError, SetJurisdictionSettingsByKeyResponse, SetJurisdictionSettingsData, SetJurisdictionSettingsError, SetJurisdictionSettingsResponse, SetMasterClientSettingsData, SetMasterClientSettingsError, SetMasterClientSettingsResponse, SetUserMasterClientsData, SetUserMasterClientsError, SetUserMasterClientsResponse, StartMigrationData, StartMigrationError, StartMigrationResponse, StopMigrationData, StopMigrationError, StopMigrationResponse, UpdateFormTemplateVersionData, UpdateFormTemplateVersionError, UpdateFormTemplateVersionResponse, VerifyMfaCodeData, VerifyMfaCodeError, VerifyMfaCodeResponse } from './types.gen';

export const client = createClient(createConfig());

/**
 * Endpoint for adding an ActivityLog to the database.
 * The Activity log will be assigned to the current user.
 *
 * Sample request:
 *
 * POST /api/audits/activitylogs.
 */
export const addActivityLog = <ThrowOnError extends boolean = false>(options?: Options<AddActivityLogData, ThrowOnError>) => {
    return (options?.client ?? client).post<AddActivityLogResponse, AddActivityLogError, ThrowOnError>({
        ...options,
        url: '/api/v1/audits/activitylogs'
    });
};

/**
 * Returns a paginated list of activitylogs.
 * Sample request:
 *
 * GET /api/audits/activitylogs?entityId=5701384D-0411-49E6-8378-D6263AAD497B&fromdate=2024-07-01&toDate=2024-10-01&pagenumber=1.
 */
export const listActivityLogs = <ThrowOnError extends boolean = false>(options?: Options<ListActivityLogsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ListActivityLogsResponse, ListActivityLogsError, ThrowOnError>({
        ...options,
        url: '/api/v1/audits/activitylogs'
    });
};

/**
 * Returns a specific activitylog.
 * Sample request:
 *
 * GET /api/audits/activitylogs/{activityLogId}.
 */
export const getActivityLog = <ThrowOnError extends boolean = false>(options: Options<GetActivityLogData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetActivityLogResponse, GetActivityLogError, ThrowOnError>({
        ...options,
        url: '/api/v1/audits/activitylogs/{activityLogId}'
    });
};

/**
 * Gets the list of announcements that match the criteria.
 * Use the GeneralSearchTerm to search in either subject or announcement body.
 *
 * Sample request:
 *
 * GET /api/v1/management/announcements.
 */
export const managementListAnnouncements = <ThrowOnError extends boolean = false>(options?: Options<ManagementListAnnouncementsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementListAnnouncementsResponse, ManagementListAnnouncementsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/announcements'
    });
};

/**
 * Create a new announcement for the application.
 * Sample request:
 *
 * POST /api/v1/management/announcements.
 */
export const managementAnnouncementCreate = <ThrowOnError extends boolean = false>(options?: Options<ManagementAnnouncementCreateData, ThrowOnError>) => {
    return (options?.client ?? client).post<ManagementAnnouncementCreateResponse, ManagementAnnouncementCreateError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/announcements'
    });
};

/**
 * Updates a new announcement for the application.
 * Sample request:
 *
 * PUT /api/v1/management/announcements/{announcementId}.
 */
export const managementAnnouncementUpdate = <ThrowOnError extends boolean = false>(options: Options<ManagementAnnouncementUpdateData, ThrowOnError>) => {
    return (options?.client ?? client).put<ManagementAnnouncementUpdateResponse, ManagementAnnouncementUpdateError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/announcements/{announcementId}'
    });
};

/**
 * Retrieve an announcement given its id.
 * Sample request:
 *
 * POST /api/v1/management/announcements/{announcementId}.
 */
export const managementAnnouncementGetById = <ThrowOnError extends boolean = false>(options: Options<ManagementAnnouncementGetByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementAnnouncementGetByIdResponse, ManagementAnnouncementGetByIdError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/announcements/{announcementId}'
    });
};

/**
 * Deletes an announcement.
 * This will delete the announcement and the related data.
 *
 * Sample request:
 *
 * DELETE /api/v1/management/announcements/{announcementId}
 * {
 *
 * }.
 */
export const managementDeleteAnnouncement = <ThrowOnError extends boolean = false>(options: Options<ManagementDeleteAnnouncementData, ThrowOnError>) => {
    return (options?.client ?? client).delete<ManagementDeleteAnnouncementResponse, ManagementDeleteAnnouncementError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/announcements/{announcementId}'
    });
};

/**
 * Upload a document for an announcement.
 * Sample request:
 * POST /api/v1/management/announcements/{announcementId}
 * {
 * data
 * }.
 */
export const managementCreateAnnouncementDocument = <ThrowOnError extends boolean = false>(options: Options<ManagementCreateAnnouncementDocumentData, ThrowOnError>) => {
    return (options?.client ?? client).post<ManagementCreateAnnouncementDocumentResponse, ManagementCreateAnnouncementDocumentError, ThrowOnError>({
        ...options,
        ...formDataBodySerializer,
        headers: {
            'Content-Type': null,
            ...options?.headers
        },
        url: '/api/v1/management/announcements/{announcementId}/documents'
    });
};

/**
 * Deletes an announcement document..
 * This will delete the announcement document given its id.
 *
 * Sample request:
 *
 * DELETE /api/v1/management/announcements/documents/{announcementDocumentId}?uploadComplete=true.
 * {
 *
 * }.
 */
export const managementDeleteAnnouncementDocument = <ThrowOnError extends boolean = false>(options: Options<ManagementDeleteAnnouncementDocumentData, ThrowOnError>) => {
    return (options?.client ?? client).delete<ManagementDeleteAnnouncementDocumentResponse, ManagementDeleteAnnouncementDocumentError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/announcements/documents/{announcementDocumentId}'
    });
};

/**
 * Returns a paginated list of audits for a given entity.
 * Sample request:
 *
 * GET /api/audits/audits?entityId=5701384D-0411-49E6-8378-D6263AAD497B&pagenumber=1.
 */
export const listAudits = <ThrowOnError extends boolean = false>(options: Options<ListAuditsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ListAuditsResponse, ListAuditsError, ThrowOnError>({
        ...options,
        url: '/api/v1/audits/audits'
    });
};

/**
 * Returns a specific audit.
 * Sample request:
 *
 * GET /api/audits/audits/{auditId}.
 */
export const getAudit = <ThrowOnError extends boolean = false>(options: Options<GetAuditData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetAuditResponse, GetAuditError, ThrowOnError>({
        ...options,
        url: '/api/v1/audits/audits/{auditId}'
    });
};

/**
 * Gets the given BeneficialOwner.
 * Sample request:
 *
 * GET /api/v1/management/beneficial-owners/{beneficialownerId}
 * .
 */
export const managementGetBeneficialOwner = <ThrowOnError extends boolean = false>(options: Options<ManagementGetBeneficialOwnerData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetBeneficialOwnerResponse, ManagementGetBeneficialOwnerError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/beneficial-owners/{beneficialownerId}'
    });
};

/**
 * Gets the given BeneficialOwner with the current and the prior version.
 * Sample request:
 *
 * GET /api/v1/management/beneficial-owners/{relationId}/comparison
 * .
 */
export const managementGetBeneficialOwnerForComparison = <ThrowOnError extends boolean = false>(options: Options<ManagementGetBeneficialOwnerForComparisonData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetBeneficialOwnerForComparisonResponse, ManagementGetBeneficialOwnerForComparisonError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/beneficial-owners/{beneficialownerId}/comparison'
    });
};

/**
 * Gets the given BeneficialOwner.
 * Sample request:
 *
 * GET /api/v1/client/beneficial-owners/{relationId}
 */
export const clientGetBeneficialOwner = <ThrowOnError extends boolean = false>(options: Options<ClientGetBeneficialOwnerData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetBeneficialOwnerResponse, ClientGetBeneficialOwnerError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/beneficial-owners/{relationId}'
    });
};

/**
 * Gets the given BeneficialOwner with the current and the prior version.
 * Sample request:
 *
 * GET /api/v1/client/beneficial-owners/{relationId}/comparison
 */
export const clientGetBeneficialOwnerForComparison = <ThrowOnError extends boolean = false>(options: Options<ClientGetBeneficialOwnerForComparisonData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetBeneficialOwnerForComparisonResponse, ClientGetBeneficialOwnerForComparisonError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/beneficial-owners/{relationId}/comparison'
    });
};

/**
 * Puts a confirmation for the BeneficialOwner.
 * Sample request:
 *
 * PUT /api/v1/client/beneficial-owners/{relationId}/confirmation
 */
export const clientConfirmBeneficialOwner = <ThrowOnError extends boolean = false>(options: Options<ClientConfirmBeneficialOwnerData, ThrowOnError>) => {
    return (options?.client ?? client).put<ClientConfirmBeneficialOwnerResponse, ClientConfirmBeneficialOwnerError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/beneficial-owners/{relationId}/confirmation'
    });
};

/**
 * Posts an update request for the BeneficialOwner.
 * Sample request:
 *
 * POST /api/v1/client/beneficial-owners/{relationId}/update-request
 * {
 * requestUpdateDTO
 * }
 *
 * UpdateRequestType is one of the following:
 *
 * MissingBeneficialOwners = 101
 * ChangeOfBeneficialOwners = 201
 * ChangeOfBeneficialOwnersAddress = 202
 * ChangeOfBeneficialOwnersParticulars = 203
 * OtherUpdateOfBeneficialOwners = 301.
 */
export const clientRequestBeneficialOwnerUpdate = <ThrowOnError extends boolean = false>(options: Options<ClientRequestBeneficialOwnerUpdateData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientRequestBeneficialOwnerUpdateResponse, ClientRequestBeneficialOwnerUpdateError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/beneficial-owners/{relationId}/update-request'
    });
};

/**
 * Simulates an update to a beneficial owner's name and returns a comparison of the changes.
 * Sample request:
 *
 * POST /api/v1/client/beneficial-owners/{relationId}/sync-simulation.
 */
export const clientBeneficialOwnerUpdateSimulation = <ThrowOnError extends boolean = false>(options: Options<ClientBeneficialOwnerUpdateSimulationData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientBeneficialOwnerUpdateSimulationResponse, ClientBeneficialOwnerUpdateSimulationError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/beneficial-owners/{relationId}/sync-simulation'
    });
};

/**
 * Gets the list of Beneficial Owners and Directors that match the criteria.
 * Sample request:
 *
 * GET /api/v1/management/bodir.
 */
export const managementListBoDirs = <ThrowOnError extends boolean = false>(options?: Options<ManagementListBoDirsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementListBoDirsResponse, ManagementListBoDirsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/bodir'
    });
};

/**
 * Exports a list of beneficial owners and directors based on the provided search criteria.
 * This endpoint allows exporting a list of beneficial owners and directors based on the specified criteria.
 * The user's identity and permissions are validated before executing the operation.
 */
export const managementDownloadBoDirList = <ThrowOnError extends boolean = false>(options?: Options<ManagementDownloadBoDirListData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementDownloadBoDirListResponse, ManagementDownloadBoDirListError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/bodir/download'
    });
};

/**
 * Gets the companies with pagination.
 * Sample request:
 *
 * GET /api/v1/management/companies?masterclientid={masterclientid}&searchTerm={search}&active=true&onboardingStatus={onboardingStatus}&pageNumber={pageNumber}&pageSize={pageSize}
 * .
 */
export const getCompanies = <ThrowOnError extends boolean = false>(options?: Options<GetCompaniesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetCompaniesResponse, GetCompaniesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies'
    });
};

/**
 * Gets the list of modules for the given company.
 * Sample request:
 *
 * GET /api/v1/management/companies/{companyId}/modules.
 */
export const getCompanyModules = <ThrowOnError extends boolean = false>(options: Options<GetCompanyModulesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetCompanyModulesResponse, GetCompanyModulesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/{companyId}/modules'
    });
};

/**
 * Set approved and enabled status for a list of modules for the given company.
 * Only the modules that are in the list will have their status changed.
 * Sample request:
 *
 * PUT /api/v1/management/companies/{companyid}/modules.
 */
export const setCompanyModules = <ThrowOnError extends boolean = false>(options: Options<SetCompanyModulesData, ThrowOnError>) => {
    return (options?.client ?? client).put<SetCompanyModulesResponse, SetCompanyModulesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/{companyId}/modules'
    });
};

/**
 * Gets a set of settings.
 * Sample request:
 *
 * GET /api/management/companies/{companyid}/settings.
 */
export const getCompanySettings = <ThrowOnError extends boolean = false>(options: Options<GetCompanySettingsData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetCompanySettingsResponse, GetCompanySettingsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/{companyId}/settings'
    });
};

/**
 * Posts a set of settings.
 * Sample request:
 *
 * POST /api/management/companies/{companyid}/settings
 * {
 *
 * }.
 */
export const setCompanySettings = <ThrowOnError extends boolean = false>(options: Options<SetCompanySettingsData, ThrowOnError>) => {
    return (options?.client ?? client).post<SetCompanySettingsResponse, SetCompanySettingsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/{companyId}/settings'
    });
};

/**
 * Posts a set of settings for a specific purpose, identified by the key.
 * Valid values for 'key':
 * - fees
 * - submissions
 *
 * Sample request:
 *
 * POST /api/management/companies/{companyid}/settings/{key}
 * {
 *
 * }.
 */
export const setCompanySettingsByKey = <ThrowOnError extends boolean = false>(options: Options<SetCompanySettingsByKeyData, ThrowOnError>) => {
    return (options?.client ?? client).post<SetCompanySettingsByKeyResponse, SetCompanySettingsByKeyError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/{companyId}/settings/{key}'
    });
};

/**
 * Gets the annual fee statuses for companies with pagination.
 * Sample request:
 * GET /api/v1/management/companies/annual-fee-status?financialYear={financialYear}&isPaid={isPaid}&searchTerm={searchTerm}&pageNumber={pageNumber}&pageSize={pageSize}.
 */
export const getCompanyAnnualFees = <ThrowOnError extends boolean = false>(options?: Options<GetCompanyAnnualFeesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetCompanyAnnualFeesResponse, GetCompanyAnnualFeesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/annual-fee-status'
    });
};

/**
 * Sets the annual fee status for multiple companies.
 * Sample request:
 * PUT /api/v1/management/companies/annual-fee-status
 * {
 * "financialYear": 2023,
 * "isPaid": true,
 * "legalEntityIds": ["guid1", "guid2", "guid3"]
 * }.
 */
export const setCompanyAnnualFeeStatus = <ThrowOnError extends boolean = false>(options?: Options<SetCompanyAnnualFeeStatusData, ThrowOnError>) => {
    return (options?.client ?? client).put<SetCompanyAnnualFeeStatusResponse, SetCompanyAnnualFeeStatusError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/annual-fee-status'
    });
};

/**
 * Sets the annual fee status for multiple financial years for a single company.
 * Only financial years that are listed are updated.
 *
 * Sample request:
 * PUT /api/v1/management/companies/{companyId}/annual-fee-status
 * {
 *
 * }.
 */
export const setCompanyAnnualFeeStatusByCompany = <ThrowOnError extends boolean = false>(options: Options<SetCompanyAnnualFeeStatusByCompanyData, ThrowOnError>) => {
    return (options?.client ?? client).put<SetCompanyAnnualFeeStatusByCompanyResponse, SetCompanyAnnualFeeStatusByCompanyError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/{companyId}/annual-fee-status'
    });
};

/**
 * Gets the annual fee status for multiple financial years for a single company.
 * Sample request:
 * GET /api/v1/management/companies/{companyId}/annual-fee-status.
 */
export const getCompanyAnnualFeeStatusByCompany = <ThrowOnError extends boolean = false>(options: Options<GetCompanyAnnualFeeStatusByCompanyData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetCompanyAnnualFeeStatusByCompanyResponse, GetCompanyAnnualFeeStatusByCompanyError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/{companyId}/annual-fee-status'
    });
};

/**
 * Sends an email related to the company.
 * Sample request:
 * POST /api/v1/management/companies/{companyId}/email
 * {
 * "RecipientEmailAddress": "",
 * "Subject": "",
 * "Body": "",
 * }.
 */
export const sendCompanyEmailMessage = <ThrowOnError extends boolean = false>(options: Options<SendCompanyEmailMessageData, ThrowOnError>) => {
    return (options?.client ?? client).post<SendCompanyEmailMessageResponse, SendCompanyEmailMessageError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/{companyId}/email'
    });
};

/**
 * Gets a single company by its ID.
 * Sample request:\
 * GET /api/v1/management/companies/{companyId}
 *
 * Possible values for OnboardingStatus:\
 * 0 = Onboarding\
 * 1 = Approved\
 * 2 = Declined\
 * .
 */
export const getCompanyById = <ThrowOnError extends boolean = false>(options: Options<GetCompanyByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetCompanyByIdResponse, GetCompanyByIdError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/{companyId}'
    });
};

/**
 * Approves a company's onboarding.
 * Sample request:
 * POST /api/v1/management/companies/{companyId}/approve
 * .
 */
export const approveCompany = <ThrowOnError extends boolean = false>(options: Options<ApproveCompanyData, ThrowOnError>) => {
    return (options?.client ?? client).post<ApproveCompanyResponse, ApproveCompanyError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/{companyId}/approve'
    });
};

/**
 * Declines a company's onboarding.
 * Sample request:
 * POST /api/v1/management/companies/{companyId}/decline
 * {
 * "declineReason": "Insufficient documentation provided"
 * }
 * .
 */
export const declineCompany = <ThrowOnError extends boolean = false>(options: Options<DeclineCompanyData, ThrowOnError>) => {
    return (options?.client ?? client).post<DeclineCompanyResponse, DeclineCompanyError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/companies/{companyId}/decline'
    });
};

/**
 * Gets the list of modules for the given company.
 * Sample request:
 *
 * GET /api/v1/client/companies/{companyid}/modules
 *
 * Only the modules that are active and enabled for the company are returned (menu display purpose).
 */
export const clientGetCompanyModules = <ThrowOnError extends boolean = false>(options: Options<ClientGetCompanyModulesData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetCompanyModulesResponse, ClientGetCompanyModulesError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/companies/{companyId}/modules'
    });
};

/**
 * Gets the list of Beneficial Owners (BOs) for the given company.
 * Sample request:
 *
 * GET /api/v1/client/companies/{companyid}/beneficial-owners
 */
export const clientGetCompanyBeneficialOwners = <ThrowOnError extends boolean = false>(options: Options<ClientGetCompanyBeneficialOwnersData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetCompanyBeneficialOwnersResponse, ClientGetCompanyBeneficialOwnersError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/companies/{companyId}/beneficial-owners'
    });
};

/**
 * Gets the list of Directors for the given company.
 * Sample request:
 *
 * GET /api/v1/client/companies/{companyid}/directors
 */
export const clientGetCompanyDirectors = <ThrowOnError extends boolean = false>(options: Options<ClientGetCompanyDirectorsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetCompanyDirectorsResponse, ClientGetCompanyDirectorsError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/companies/{companyId}/directors'
    });
};

/**
 * Posts an assistance request for the company.
 * Sample request:
 *
 * POST /api/v1/client/companies/{companyid}/assistance-request
 *
 * Possible types of request:
 *
 * NoBeneficialOwner = 101
 * NoDirector = 201
 * NoShareholder = 301.
 */
export const clientRequestAssistance = <ThrowOnError extends boolean = false>(options: Options<ClientRequestAssistanceData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientRequestAssistanceResponse, ClientRequestAssistanceError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/companies/{companyId}/assistance-request'
    });
};

/**
 * Posts a new submission for the company based on the module and year.
 * Sample request:
 *
 * POST /api/v1/client/companies/{companyid}/modules/{moduleId}/submissions?year=2024
 */
export const clientCreateSubmission = <ThrowOnError extends boolean = false>(options: Options<ClientCreateSubmissionData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientCreateSubmissionResponse, ClientCreateSubmissionError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/companies/{companyId}/modules/{moduleId}/submissions'
    });
};

/**
 * Gets the list of submissions for the given company for a specific module.
 * Sample request:
 *
 * GET /api/v1/client/companies/{companyid}/modules/{moduleId}/submissions
 * .
 */
export const clientGetCompanyModuleSubmissions = <ThrowOnError extends boolean = false>(options: Options<ClientGetCompanyModuleSubmissionsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetCompanyModuleSubmissionsResponse, ClientGetCompanyModuleSubmissionsError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/companies/{companyId}/modules/{moduleId}/submissions'
    });
};

/**
 * Gets the list of years to show when about to start a submission.
 *  Sample request:
 *
 * GET /api/v1/client/companies/{companyid}/modules/{moduleId}/submission-years
 * .
 */
export const clientGetAvailableSubmissionYears = <ThrowOnError extends boolean = false>(options: Options<ClientGetAvailableSubmissionYearsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetAvailableSubmissionYearsResponse, ClientGetAvailableSubmissionYearsError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/companies/{companyId}/modules/{moduleId}/submission-years'
    });
};

/**
 * Gets all currencies with pagination and optional filters.
 * Sample request:
 *
 * GET /api/management/currencies?name={name}&pageNumber={pageNumber}&pageSize={pageSize}.
 */
export const getAllCurrencies = <ThrowOnError extends boolean = false>(options?: Options<GetAllCurrenciesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetAllCurrenciesResponse, GetAllCurrenciesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/currencies'
    });
};

/**
 * Creates a currency.
 * Sample request:
 *
 * POST /api/management/currencies
 * {
 * "name": "Dollar",
 * "code": "USD",
 * "symbol": "$"
 * }.
 */
export const createCurrency = <ThrowOnError extends boolean = false>(options?: Options<CreateCurrencyData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateCurrencyResponse, CreateCurrencyError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/currencies'
    });
};

/**
 * Deletes a currency by its ID.
 * Sample request:
 *
 * DELETE /api/management/currencies/{id}.
 */
export const deleteCurrency = <ThrowOnError extends boolean = false>(options: Options<DeleteCurrencyData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteCurrencyResponse, DeleteCurrencyError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/currencies/{id}'
    });
};

/**
 * Gets the given director.
 * Sample request:
 *
 * GET /api/v1/management/directors/{directorId}.
 */
export const managementGetDirector = <ThrowOnError extends boolean = false>(options: Options<ManagementGetDirectorData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetDirectorResponse, ManagementGetDirectorError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/directors/{directorId}'
    });
};

/**
 * Gets the given director with the current and the prior version.
 * Sample request:
 *
 * GET /api/v1/management/directors/{directorId}/comparison
 * .
 */
export const managementGetDirectorForComparison = <ThrowOnError extends boolean = false>(options: Options<ManagementGetDirectorForComparisonData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetDirectorForComparisonResponse, ManagementGetDirectorForComparisonError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/directors/{directorId}/comparison'
    });
};

/**
 * Gets the given Director.
 * Sample request:
 *
 * GET /api/v1/client/directors/{relationId}
 */
export const clientGetDirector = <ThrowOnError extends boolean = false>(options: Options<ClientGetDirectorData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetDirectorResponse, ClientGetDirectorError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/directors/{relationId}'
    });
};

/**
 * Gets the given Director with the current and the prior version.
 * Sample request:
 *
 * GET /api/v1/client/directors/{relationId}/comparison
 */
export const clientGetDirectorForComparison = <ThrowOnError extends boolean = false>(options: Options<ClientGetDirectorForComparisonData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetDirectorForComparisonResponse, ClientGetDirectorForComparisonError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/directors/{relationId}/comparison'
    });
};

/**
 * Puts a confirmation for the Director.
 * Sample request:
 *
 * PUT /api/v1/client/directors/{relationId}/confirmation
 */
export const clientConfirmDirector = <ThrowOnError extends boolean = false>(options: Options<ClientConfirmDirectorData, ThrowOnError>) => {
    return (options?.client ?? client).put<ClientConfirmDirectorResponse, ClientConfirmDirectorError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/directors/{relationId}/confirmation'
    });
};

/**
 * Posts an update request for the Director.
 * Sample request:
 *
 * POST /api/v1/client/directors/{relationId}/update-request
 * {
 * requestUpdateDTO
 * }
 *
 * UpdateRequestType is one of the following:
 *
 * MissingDirectors = 102
 * ChangeOfDirectors = 211
 * ChangeOfDirectorsAddress = 212
 * ChangeOfDirectorsParticulars = 213
 * OtherUpdateOfDirectors = 302.
 */
export const clientRequestDirectorUpdate = <ThrowOnError extends boolean = false>(options: Options<ClientRequestDirectorUpdateData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientRequestDirectorUpdateResponse, ClientRequestDirectorUpdateError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/directors/{relationId}/update-request'
    });
};

export const clientDirecctorUpdateSimulation = <ThrowOnError extends boolean = false>(options: Options<ClientDirecctorUpdateSimulationData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientDirecctorUpdateSimulationResponse, ClientDirecctorUpdateSimulationError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/directors/{relationId}/sync-simulation'
    });
};

/**
 * Upload a document.
 * Sample request:
 * POST /api/v1/common/documents
 * {
 * data
 * }.
 */
export const createDocument = <ThrowOnError extends boolean = false>(options?: Options<CreateDocumentData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateDocumentResponse, CreateDocumentError, ThrowOnError>({
        ...options,
        ...formDataBodySerializer,
        headers: {
            'Content-Type': null,
            ...options?.headers
        },
        url: '/api/v1/common/documents'
    });
};

/**
 * Retreive a list of documents given a list of Guid containing their id.
 * Sample request:
 * GET /api/v1/common/documents&documentIds={documentId}.
 */
export const getApiV1CommonDocuments = <ThrowOnError extends boolean = false>(options?: Options<GetApiV1CommonDocumentsData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetApiV1CommonDocumentsResponse, GetApiV1CommonDocumentsError, ThrowOnError>({
        ...options,
        url: '/api/v1/common/documents'
    });
};

/**
 * Retrieve a document given its id.
 * Sample request:
 * GET /api/v1/common/documents/{documentId}.
 */
export const getApiV1CommonDocumentsByDocumentId = <ThrowOnError extends boolean = false>(options: Options<GetApiV1CommonDocumentsByDocumentIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetApiV1CommonDocumentsByDocumentIdResponse, GetApiV1CommonDocumentsByDocumentIdError, ThrowOnError>({
        ...options,
        url: '/api/v1/common/documents/{documentId}'
    });
};

/**
 * Gets a list with form templates.
 * Sample request:
 *
 * GET /api/v1/management/form-templates
 */
export const getFormTemplates = <ThrowOnError extends boolean = false>(options?: Options<GetFormTemplatesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetFormTemplatesResponse, GetFormTemplatesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/form-templates'
    });
};

/**
 * Gets the given form template.
 * Sample request:
 *
 * GET /api/v1/management/form-templates/{templateid}
 */
export const getFormTemplate = <ThrowOnError extends boolean = false>(options: Options<GetFormTemplateData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetFormTemplateResponse, GetFormTemplateError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/form-templates/{templateId}'
    });
};

/**
 * Posts a new version for the form template.
 * Sample request:
 *
 * POST /api/v1/management/form-templates/{templateid}/template-versions
 */
export const createFormTemplateVersion = <ThrowOnError extends boolean = false>(options: Options<CreateFormTemplateVersionData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateFormTemplateVersionResponse, CreateFormTemplateVersionError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/form-templates/{templateId}/template-versions'
    });
};

/**
 * Puts an update for the version for the form template.
 * Sample request:
 *
 * PUT /api/v1/management/form-templates/{templateid}/template-versions/{versionId}
 */
export const updateFormTemplateVersion = <ThrowOnError extends boolean = false>(options: Options<UpdateFormTemplateVersionData, ThrowOnError>) => {
    return (options?.client ?? client).post<UpdateFormTemplateVersionResponse, UpdateFormTemplateVersionError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/form-templates/{templateId}/template-versions/{versionId}'
    });
};

/**
 * Returns the info for the inbox.
 * Sample request:
 *
 * GET /api/v1/client/inbox.
 */
export const getInboxInfo = <ThrowOnError extends boolean = false>(options?: Options<GetInboxInfoData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetInboxInfoResponse, GetInboxInfoError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/inbox'
    });
};

/**
 * Get inbox messages with pagination.
 * This endpoint retrieves a paginated list of messages for the given user with the specified page number and page size.
 * Sample request:
 *
 * GET /api/v1/client/inbox/messages?userId={userId}&pagenumber=1&pagesize=20.
 */
export const getInboxMessages = <ThrowOnError extends boolean = false>(options?: Options<GetInboxMessagesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetInboxMessagesResponse, GetInboxMessagesError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/inbox/messages'
    });
};

/**
 * Gets the given inbox message.
 * Sample request:
 *
 * GET /api/v1/client/inbox/messages/{messageId}.
 */
export const getInboxMessage = <ThrowOnError extends boolean = false>(options: Options<GetInboxMessageData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetInboxMessageResponse, GetInboxMessageError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/inbox/messages/{messageId}'
    });
};

/**
 * Creates a message read status.
 * Sample request:
 * POST /api/v1/client/inbox/messages/{messageId}/readstatus.
 */
export const createMessageReadStatus = <ThrowOnError extends boolean = false>(options: Options<CreateMessageReadStatusData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateMessageReadStatusResponse, CreateMessageReadStatusError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/inbox/messages/{messageId}/readstatus'
    });
};

/**
 * Deletes a message read status for the user by the message ID.
 * Sample request:
 * DELETE /api/v1/client/inbox/messages/{messageId}/readstatus.
 */
export const deleteInboxReadStatus = <ThrowOnError extends boolean = false>(options: Options<DeleteInboxReadStatusData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteInboxReadStatusResponse, DeleteInboxReadStatusError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/inbox/messages/{messageId}/readstatus'
    });
};

/**
 * Gets an invoice by its ID.
 * Sample request:
 *
 * GET /api/v1/management/invoices/{id}
 *
 * Sample response:
 *
 * {
 * "id": "a0f43f9c-2d1c-4d4e-b74d-e89cc5fbe1c7",
 * "companyName": "Acme Inc.",
 * "amount": 1000.00,
 * "financialYear": 2023,
 * "incorporationNr": "ABC123",
 * "file": "invoice_123.pdf",
 * "status": 0,
 * "currencySymbol": "$",
 * "paidDate": "2023-05-18T10:30:00Z",
 * "transactionId": 987654,
 * "txId": "TX98765",
 * "currencyId": "6ca5809f-06a3-4e99-9e0a-df570d3a482f",
 * "layout": "TRIDENTTRUST",
 * "invoiceLines": [
 * {
 * "invoiceId": "a0f43f9c-2d1c-4d4e-b74d-e89cc5fbe1c7",
 * "currencyId": "d1b8f5c3-3c3f-4f32-9d1f-a56450e77c1e",
 * "description": "Late filing fee",
 * "sequence": 2,
 * "amount": 75.00,
 * "id": "504c0de4-2ba9-41ad-825c-b45d25f9bf6b"
 * },
 * {
 * "invoiceId": "a0f43f9c-2d1c-4d4e-b74d-e89cc5fbe1c7",
 * "currencyId": "d1b8f5c3-3c3f-4f32-9d1f-a56450e77c1e",
 * "description": "Submission fee for year 2020",
 * "sequence": 1,
 * "amount": 200.00,
 * "id": "415dced3-d7e4-43d3-b0c8-ce9160976c2a"
 * }
 * ]
 * }
 */
export const managementGetInvoiceById = <ThrowOnError extends boolean = false>(options: Options<ManagementGetInvoiceByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetInvoiceByIdResponse, ManagementGetInvoiceByIdError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/invoices/{id}'
    });
};

/**
 * Gets a paged list of invoices based on the provided request parameters.
 * Sample request:
 *
 * GET /api/v1/client/Invoices?pageNumber=1&pageSize=10
 *
 * Sample response:
 *
 * {
 * "items": [
 * {
 * "id": "a0f43f9c-2d1c-4d4e-b74d-e89cc5fbe1c7",
 * "companyName": "Acme Inc.",
 * "amount": 1000.00,
 * "financialYear": 2023,
 * "incorporationNr": "ABC123",
 * "file": "invoice_123.pdf",
 * "CurrencyId": "d1b8f5c3-3c3f-4f32-9d1f-a56450e77c1e",
 * "status": 0,
 * "currencySymbol": "$",
 * "paidDate": "2023-05-18T10:30:00Z",
 * "transactionId": 987654,
 * "txId": "TX98765",
 * "layout": "TRIDENTTRUST"
 * },
 * ...
 * ],
 * "pageNumber": 1,
 * "totalPages": 3,
 * "totalCount": 25,
 * "pageSize": 10,
 * "hasPreviousPage": false,
 * "hasNextPage": true
 * }.
 */
export const clientGetInvoices = <ThrowOnError extends boolean = false>(options?: Options<ClientGetInvoicesData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetInvoicesResponse, ClientGetInvoicesError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/invoices'
    });
};

/**
 * Gets an invoice by its ID.
 * Sample request:
 *
 * GET /api/v1/client/invoices/{id}
 *
 * Sample response:
 *
 * {
 * "id": "a0f43f9c-2d1c-4d4e-b74d-e89cc5fbe1c7",
 * "companyName": "Acme Inc.",
 * "amount": 1000.00,
 * "financialYear": 2023,
 * "incorporationNr": "ABC123",
 * "file": "invoice_123.pdf",
 * "status": 0,
 * "currencySymbol": "$",
 * "paidDate": "2023-05-18T10:30:00Z",
 * "transactionId": 987654,
 * "txId": "TX98765",
 * "currencyId": "6ca5809f-06a3-4e99-9e0a-df570d3a482f",
 * "layout": "TRIDENTTRUST",
 * "invoiceLines": [
 * {
 * "invoiceId": "a0f43f9c-2d1c-4d4e-b74d-e89cc5fbe1c7",
 * "currencyId": "d1b8f5c3-3c3f-4f32-9d1f-a56450e77c1e",
 * "description": "Late filing fee",
 * "sequence": 2,
 * "amount": 75.00,
 * "id": "504c0de4-2ba9-41ad-825c-b45d25f9bf6b"
 * },
 * {
 * "invoiceId": "a0f43f9c-2d1c-4d4e-b74d-e89cc5fbe1c7",
 * "currencyId": "d1b8f5c3-3c3f-4f32-9d1f-a56450e77c1e",
 * "description": "Submission fee for year 2020",
 * "sequence": 1,
 * "amount": 200.00,
 * "id": "415dced3-d7e4-43d3-b0c8-ce9160976c2a"
 * }
 * ]
 * }
 */
export const clientGetInvoiceById = <ThrowOnError extends boolean = false>(options: Options<ClientGetInvoiceByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetInvoiceByIdResponse, ClientGetInvoiceByIdError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/invoices/{id}'
    });
};

/**
 * Gets the jurisdictions with pagination.
 * Sample request:
 *
 * GET /api/management/jurisdictions?pageNumber={pageNumber}&pageSize={pageSize}.
 */
export const getJurisdictions = <ThrowOnError extends boolean = false>(options?: Options<GetJurisdictionsData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetJurisdictionsResponse, GetJurisdictionsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/jurisdictions'
    });
};

/**
 * Gets the list of modules for the given jurisdiction.
 * Sample request:
 *
 * GET /api/v1/management/jurisdictions/{jurisdictionid}/modules
 */
export const getJurisdictionModules = <ThrowOnError extends boolean = false>(options: Options<GetJurisdictionModulesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetJurisdictionModulesResponse, GetJurisdictionModulesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/jurisdictions/{jurisdictionId}/modules'
    });
};

/**
 * Sets the list of modules for the given jurisdiction.
 * Sample request:
 *
 * PUT /api/v1/management/jurisdictions/{jurisdictionid}/modules
 */
export const setJurisdictionModules = <ThrowOnError extends boolean = false>(options: Options<SetJurisdictionModulesData, ThrowOnError>) => {
    return (options?.client ?? client).put<SetJurisdictionModulesResponse, SetJurisdictionModulesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/jurisdictions/{jurisdictionId}/modules'
    });
};

/**
 * Gets tax rates for a specific jurisdiction.
 * Sample request:
 *
 * GET /api/v1/management/jurisdictions/{jurisdictionid}/tax-rates
 */
export const getJurisdictionTaxRates = <ThrowOnError extends boolean = false>(options: Options<GetJurisdictionTaxRatesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetJurisdictionTaxRatesResponse, GetJurisdictionTaxRatesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/jurisdictions/{jurisdictionId}/tax-rates'
    });
};

/**
 * Gets a set of settings.
 * Sample request:
 *
 * GET /api/management/jurisdictions/{jurisdictionId}/settings.
 */
export const getJurisdictionSettings = <ThrowOnError extends boolean = false>(options: Options<GetJurisdictionSettingsData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetJurisdictionSettingsResponse, GetJurisdictionSettingsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/jurisdictions/{jurisdictionId}/settings'
    });
};

/**
 * Posts a set of settings.
 * Sample request:
 *
 * POST /api/management/jurisdictions/{jurisdictionid}/settings
 * {
 *
 * }.
 */
export const setJurisdictionSettings = <ThrowOnError extends boolean = false>(options: Options<SetJurisdictionSettingsData, ThrowOnError>) => {
    return (options?.client ?? client).post<SetJurisdictionSettingsResponse, SetJurisdictionSettingsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/jurisdictions/{jurisdictionId}/settings'
    });
};

/**
 * Posts a set of settings for a specific purpose, identied by the type.
 * Valid values for 'key':
 * - documents
 * - str-late-payment-fees
 * - fees
 *
 * Sample request:
 *
 * POST /api/management/jurisdictions/{jurisdictionid}/settings/{key}
 * {
 *
 * }
 */
export const setJurisdictionSettingsByKey = <ThrowOnError extends boolean = false>(options: Options<SetJurisdictionSettingsByKeyData, ThrowOnError>) => {
    return (options?.client ?? client).post<SetJurisdictionSettingsByKeyResponse, SetJurisdictionSettingsByKeyError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/jurisdictions/{jurisdictionId}/settings/{key}'
    });
};

/**
 * Gets the list of form templates for the given jurisdiction.
 * Sample request:
 *
 * GET /api/v1/management/jurisdictions/{jurisdictionid}/form-templates
 */
export const getJurisdictionFormTemplates = <ThrowOnError extends boolean = false>(options: Options<GetJurisdictionFormTemplatesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetJurisdictionFormTemplatesResponse, GetJurisdictionFormTemplatesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/jurisdictions/{jurisdictionId}/form-templates'
    });
};

/**
 * Gets tax rates for a specific jurisdiction with pagination.
 * Sample request:
 *
 * GET /api/v1/management/jurisdiction-tax-rates?jurisdictionId={jurisdictionId}?pageNumber={pageNumber}&pageSize={pageSize}.
 */
export const getTaxRatesByJurisdiction = <ThrowOnError extends boolean = false>(options?: Options<GetTaxRatesByJurisdictionData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetTaxRatesByJurisdictionResponse, GetTaxRatesByJurisdictionError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/jurisdiction-tax-rates'
    });
};

/**
 * Creates or updates a tax rate for a specific jurisdiction.
 * Sample request:
 *
 * POST /api/v1/management/jurisdiction-tax-rates
 * {
 * "jurisdictionId": "Guid",
 * "rate": 0.10,
 * "startDate": "2023-01-01T00:00:00Z"
 * }.
 */
export const createOrUpdateTaxRate = <ThrowOnError extends boolean = false>(options?: Options<CreateOrUpdateTaxRateData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateOrUpdateTaxRateResponse, CreateOrUpdateTaxRateError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/jurisdiction-tax-rates'
    });
};

/**
 * Deletes a tax rate by its ID.
 * Sample request:
 *
 * DELETE /api/v1/management/jurisdiction-tax-rates/{id}.
 */
export const deleteTaxRate = <ThrowOnError extends boolean = false>(options: Options<DeleteTaxRateData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteTaxRateResponse, DeleteTaxRateError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/jurisdiction-tax-rates/{jurisdictionTaxRateId}'
    });
};

/**
 * Gets a single master client.
 * Sample request:
 *
 * GET /api/management/master-clients/{masterclientId}.
 */
export const getMasterClient = <ThrowOnError extends boolean = false>(options: Options<GetMasterClientData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetMasterClientResponse, GetMasterClientError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/master-clients/{masterclientId}'
    });
};

/**
 * Gets the master clients with pagination.
 * Sample request:
 *
 * GET /api/management/master-clients?searchTerm={searchTerm}&jurisdictionid={jurisdictionid}&pageNumber={pageNumber}&pageSize={pageSize}.
 */
export const getMasterClients = <ThrowOnError extends boolean = false>(options?: Options<GetMasterClientsData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetMasterClientsResponse, GetMasterClientsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/master-clients'
    });
};

/**
 * Gets the companies with pagination for the master client.
 * Sample request:
 *
 * GET /api/management/master-clients/{masterclientid}/companies?pageNumber={pageNumber}&pageSize={pageSize}.
 *
 * Possible values for OnboardingStatus:\
 * 0 =  Onboarding\
 * 1 =  Approved\
 * 2 =  Declined\
 * .
 */
export const getMasterClientCompanies = <ThrowOnError extends boolean = false>(options: Options<GetMasterClientCompaniesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetMasterClientCompaniesResponse, GetMasterClientCompaniesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/master-clients/{masterclientId}/companies'
    });
};

/**
 * Gets the users with pagination for the master client.
 * Each user has information about its registration and invitation status.
 *
 * Sample request:
 *
 * GET /api/management/master-clients/{masterclientid}/users?pageNumber={pageNumber}&pageSize={pageSize}.
 */
export const getMasterClientUsers = <ThrowOnError extends boolean = false>(options: Options<GetMasterClientUsersData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetMasterClientUsersResponse, GetMasterClientUsersError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/master-clients/{masterclientId}/users'
    });
};

/**
 * Adds a user by emailaddress to a master client.
 * Sample request:
 *
 * POST /api/management/master-clients/{masterclientId}/users
 * {
 * "emailAddress": "<EMAIL>"
 * }.
 */
export const addUserToMasterClient = <ThrowOnError extends boolean = false>(options: Options<AddUserToMasterClientData, ThrowOnError>) => {
    return (options?.client ?? client).post<AddUserToMasterClientResponse, AddUserToMasterClientError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/master-clients/{masterclientId}/users'
    });
};

/**
 * Gets a set of settings for a specific purpose, identied by the key.
 * Sample request:
 *
 * GET /api/management/master-clients/{masterclientid}/settings?key=fees.
 */
export const getMasterClientSettings = <ThrowOnError extends boolean = false>(options: Options<GetMasterClientSettingsData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetMasterClientSettingsResponse, GetMasterClientSettingsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/master-clients/{masterclientId}/settings'
    });
};

/**
 * Posts a set of settings for a specific purpose, identied by the key.
 * Sample request:
 *
 * POST /api/management/master-clients/{masterclientid}/settings?key=fees
 * {
 *
 * }.
 */
export const setMasterClientSettings = <ThrowOnError extends boolean = false>(options: Options<SetMasterClientSettingsData, ThrowOnError>) => {
    return (options?.client ?? client).post<SetMasterClientSettingsResponse, SetMasterClientSettingsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/master-clients/{masterclientId}/settings'
    });
};

/**
 * Removes a user from a master client.
 * Sample request:
 *
 * DELETE /api/management/master-clients/{masterclientId}/users/{userId}.
 */
export const removeUserFromMasterClient = <ThrowOnError extends boolean = false>(options: Options<RemoveUserFromMasterClientData, ThrowOnError>) => {
    return (options?.client ?? client).delete<RemoveUserFromMasterClientResponse, RemoveUserFromMasterClientError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/master-clients/{masterclientId}/users/{userId}'
    });
};

/**
 * Imports an excel sheet with master client data and email addresses.
 */
export const postApiV1ImportsMasterclients = <ThrowOnError extends boolean = false>(options?: Options<PostApiV1ImportsMasterclientsData, ThrowOnError>) => {
    return (options?.client ?? client).post<void, unknown, ThrowOnError>({
        ...options,
        ...formDataBodySerializer,
        headers: {
            'Content-Type': null,
            ...options?.headers
        },
        url: '/api/v1/imports/masterclients'
    });
};

/**
 * Gets a list of masterclients with the jurisdiction info as a result of a search on both MasterClient Code and Jurisdiction name.
 * Sample request:
 *
 * GET /api/v1/client/master-clients?search={search}.
 */
export const clientGetMasterClients = <ThrowOnError extends boolean = false>(options?: Options<ClientGetMasterClientsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetMasterClientsResponse, ClientGetMasterClientsError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/master-clients'
    });
};

/**
 * Gets a list of companies with the 'Approved' onboardingstatus for the given masterclient.
 * Sample request:
 *
 * GET /api/v1/client/master-clients/{masterclientid}/companies?search={search}&active=true.
 */
export const clientGetMasterClientCompanies = <ThrowOnError extends boolean = false>(options: Options<ClientGetMasterClientCompaniesData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetMasterClientCompaniesResponse, ClientGetMasterClientCompaniesError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/master-clients/{masterClientId}/companies'
    });
};

/**
 * Gets the list of submissions for the given master client for a specific module.
 * Sample request:
 *
 * GET /api/v1/client/master-clients/{masterClientId}/submissions
 * .
 */
export const clientGetMasterClientSubmissions = <ThrowOnError extends boolean = false>(options: Options<ClientGetMasterClientSubmissionsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetMasterClientSubmissionsResponse, ClientGetMasterClientSubmissionsError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/master-clients/{masterClientId}/submissions'
    });
};

/**
 * Sends an email.
 * Sample request:
 * POST /api/v1/management/communication/messages/email
 * {
 * "RecipientEmailAddress": "",
 * "Subject": "",
 * "Body": "",
 * }.
 */
export const sendEmailMessage = <ThrowOnError extends boolean = false>(options?: Options<SendEmailMessageData, ThrowOnError>) => {
    return (options?.client ?? client).post<SendEmailMessageResponse, SendEmailMessageError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/communication/messages/email'
    });
};

/**
 * Gets the MFA Info for test user using authenticator.
 */
export const getApiV1ToolsMfaMfaInfoAuthenticator = <ThrowOnError extends boolean = false>(options?: Options<GetApiV1ToolsMfaMfaInfoAuthenticatorData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetApiV1ToolsMfaMfaInfoAuthenticatorResponse, GetApiV1ToolsMfaMfaInfoAuthenticatorError, ThrowOnError>({
        ...options,
        url: '/api/v1/tools/mfa/mfa-info-authenticator'
    });
};

/**
 * Gets the MFA Info for test user using email.
 */
export const getApiV1ToolsMfaMfaInfoEmail = <ThrowOnError extends boolean = false>(options?: Options<GetApiV1ToolsMfaMfaInfoEmailData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetApiV1ToolsMfaMfaInfoEmailResponse, GetApiV1ToolsMfaMfaInfoEmailError, ThrowOnError>({
        ...options,
        url: '/api/v1/tools/mfa/mfa-info-email'
    });
};

/**
 * Gets the MFA Info for test user using email.
 */
export const getApiV1ToolsMfaMfaVerification = <ThrowOnError extends boolean = false>(options?: Options<GetApiV1ToolsMfaMfaVerificationData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetApiV1ToolsMfaMfaVerificationResponse, GetApiV1ToolsMfaMfaVerificationError, ThrowOnError>({
        ...options,
        url: '/api/v1/tools/mfa/mfa-verification'
    });
};

/**
 * Gets the method for MFA for the user.
 * Returns an empty string if the method is not set yet.
 *
 * Sample request:
 *
 * GET /api/security/mfa/users/{userId}/method.
 */
export const getMfaMethod = <ThrowOnError extends boolean = false>(options: Options<GetMfaMethodData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetMfaMethodResponse, GetMfaMethodError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/mfa/users/{userId}/method'
    });
};

/**
 * Sets the method for MFA for the user.
 * Returns an MFAInfo with the codes to use for configuring the authenticator.
 *
 * Sample request:
 *
 * PUT     /api/security/mfa/users/{userId}/method.
 *
 * Possible values are:
 * * authenticator
 * * email-code.
 */
export const putMfaMethod = <ThrowOnError extends boolean = false>(options: Options<PutMfaMethodData, ThrowOnError>) => {
    return (options?.client ?? client).put<PutMfaMethodResponse, PutMfaMethodError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/mfa/users/{userId}/method'
    });
};

/**
 * Sets the method for MFA for the user.
 * Returns an MFAInfo with the codes to use for configuring the authenticator.
 *
 * Sample request:
 *
 * PATCH   /api/security/mfa/users/{userId}/method.
 *
 * Possible values are:
 * * authenticator
 * * email-code.
 */
export const patchMfaMethod = <ThrowOnError extends boolean = false>(options: Options<PatchMfaMethodData, ThrowOnError>) => {
    return (options?.client ?? client).patch<PatchMfaMethodResponse, PatchMfaMethodError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/mfa/users/{userId}/method'
    });
};

/**
 * Resets/deletes the MFA info for the given user (for management only).
 * After this, the method to use must be set again and in case of an authenticator it must be re-configured.
 *
 * Sample request:
 *
 * DELETE  /api/security/mfa/users/{userId}.
 */
export const resetMfaMethod = <ThrowOnError extends boolean = false>(options: Options<ResetMfaMethodData, ThrowOnError>) => {
    return (options?.client ?? client).delete<ResetMfaMethodResponse, ResetMfaMethodError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/mfa/users/{userId}'
    });
};

/**
 * Posts a requests for reset of the MFA of the user.
 * The user will receive an email with a code that needs to be used for cofirmation of the reset.
 *
 * Sample request:
 *
 * POST /api/security/mfa/users/{userId}/reset-request.
 */
export const requestMfaReset = <ThrowOnError extends boolean = false>(options: Options<RequestMfaResetData, ThrowOnError>) => {
    return (options?.client ?? client).post<RequestMfaResetResponse, RequestMfaResetError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/mfa/users/{userId}/reset-request'
    });
};

/**
 * Posts the confirmation for reset of the MFA of the user.
 * The user must enter the code that was received by email.
 * After this, the method to use must be set again and in case of an authenticator it must be re-configured.
 *
 * Sample request:
 *
 * POST /api/security/mfa/users/{userId}/reset-confirmation?code={code}.
 */
export const confirmMfaReset = <ThrowOnError extends boolean = false>(options: Options<ConfirmMfaResetData, ThrowOnError>) => {
    return (options?.client ?? client).post<ConfirmMfaResetResponse, ConfirmMfaResetError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/mfa/users/{userId}/reset-confirmation'
    });
};

/**
 * Sends an email to the user with a verification code for MFA.
 * Returns an MFAInfo with the expiration date for the code.
 *
 * Sample request:
 *
 * POST    /api/security/mfa/users/{userId}/email.
 */
export const sendMfaEmail = <ThrowOnError extends boolean = false>(options: Options<SendMfaEmailData, ThrowOnError>) => {
    return (options?.client ?? client).post<SendMfaEmailResponse, SendMfaEmailError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/mfa/users/{userId}/email'
    });
};

/**
 * Verifies the code that the user entered for the MFA.
 * Sample request:
 *
 * POST    /api/security/mfa/users/{userId}/verification?code={code}.
 */
export const verifyMfaCode = <ThrowOnError extends boolean = false>(options: Options<VerifyMfaCodeData, ThrowOnError>) => {
    return (options?.client ?? client).post<VerifyMfaCodeResponse, VerifyMfaCodeError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/mfa/users/{userId}/verification'
    });
};

/**
 * Starts a data migration for the TNEV region asynchronously.
 * Sample request:
 *
 * POST /api/v1/migration/start.
 */
export const startMigration = <ThrowOnError extends boolean = false>(options?: Options<StartMigrationData, ThrowOnError>) => {
    return (options?.client ?? client).post<StartMigrationResponse, StartMigrationError, ThrowOnError>({
        ...options,
        url: '/api/v1/migration/start'
    });
};

/**
 * Gets the status of the data migration for the region, currently hardcoded to TNEV.
 * Sample request:
 *
 * GET /api/v1/migration/status.
 */
export const getMigrationStatus = <ThrowOnError extends boolean = false>(options?: Options<GetMigrationStatusData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetMigrationStatusResponse, GetMigrationStatusError, ThrowOnError>({
        ...options,
        url: '/api/v1/migration/status'
    });
};

/**
 * Stops the data migration for the TNEV region.
 * Sample request:
 *
 * POST /api/v1/migration/stop.
 */
export const stopMigration = <ThrowOnError extends boolean = false>(options?: Options<StopMigrationData, ThrowOnError>) => {
    return (options?.client ?? client).post<StopMigrationResponse, StopMigrationError, ThrowOnError>({
        ...options,
        url: '/api/v1/migration/stop'
    });
};

/**
 * Does a post-migration cleanup for the TNEV region.
 * Sample request:
 *
 * POST /api/v1/migration/cleanup.
 */
export const postMigrationCleanup = <ThrowOnError extends boolean = false>(options?: Options<PostMigrationCleanupData, ThrowOnError>) => {
    return (options?.client ?? client).post<PostMigrationCleanupResponse, PostMigrationCleanupError, ThrowOnError>({
        ...options,
        url: '/api/v1/migration/cleanup'
    });
};

/**
 * Gets the log workbook of the data migration for the region, currently hardcoded to TNEV.
 * Sample request:
 *
 * GET /api/v1/migration/log-workbook.
 */
export const getMigrationLogWorkbook = <ThrowOnError extends boolean = false>(options?: Options<GetMigrationLogWorkbookData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetMigrationLogWorkbookResponse, GetMigrationLogWorkbookError, ThrowOnError>({
        ...options,
        url: '/api/v1/migration/log-workbook'
    });
};

/**
 * Gets the available modules.
 * Sample request:
 *
 * GET /api/v1/management/modules?active=true.
 */
export const getModules = <ThrowOnError extends boolean = false>(options?: Options<GetModulesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetModulesResponse, GetModulesError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/modules'
    });
};

/**
 * Gets the list of years to show when searching submissions.
 *  Sample request:
 *
 * GET /api/v1/management/modules/{moduleId}/submission-years
 * .
 */
export const managementGetAllSubmissionYears = <ThrowOnError extends boolean = false>(options: Options<ManagementGetAllSubmissionYearsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetAllSubmissionYearsResponse, ManagementGetAllSubmissionYearsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/modules/{moduleId}/submission-years'
    });
};

/**
 * The endpoint for a submit in the Signup flow (custom extension).
 */
export const postApiV1ExternalidOnAttributeCollectionStart = <ThrowOnError extends boolean = false>(options?: Options<PostApiV1ExternalidOnAttributeCollectionStartData, ThrowOnError>) => {
    return (options?.client ?? client).post<PostApiV1ExternalidOnAttributeCollectionStartResponse, PostApiV1ExternalidOnAttributeCollectionStartError, ThrowOnError>({
        ...options,
        url: '/api/v1/externalid/on-attribute-collection-start'
    });
};

/**
 * The endpoint for a submit in the Signup flow (custom extension).
 */
export const postOnattributecollectionstart = <ThrowOnError extends boolean = false>(options?: Options<PostOnattributecollectionstartData, ThrowOnError>) => {
    return (options?.client ?? client).post<PostOnattributecollectionstartResponse, PostOnattributecollectionstartError, ThrowOnError>({
        ...options,
        url: '/onattributecollectionstart'
    });
};

/**
 * The endpoint for a submit in the Signup flow (custom extension).
 */
export const postApiV1ExternalidOnAttributeCollectionSubmit = <ThrowOnError extends boolean = false>(options?: Options<PostApiV1ExternalidOnAttributeCollectionSubmitData, ThrowOnError>) => {
    return (options?.client ?? client).post<PostApiV1ExternalidOnAttributeCollectionSubmitResponse, PostApiV1ExternalidOnAttributeCollectionSubmitError, ThrowOnError>({
        ...options,
        url: '/api/v1/externalid/on-attribute-collection-submit'
    });
};

/**
 * The endpoint for a submit in the Signup flow (custom extension).
 */
export const postOnattributecollectionsubmit = <ThrowOnError extends boolean = false>(options?: Options<PostOnattributecollectionsubmitData, ThrowOnError>) => {
    return (options?.client ?? client).post<PostOnattributecollectionsubmitResponse, PostOnattributecollectionsubmitError, ThrowOnError>({
        ...options,
        url: '/onattributecollectionsubmit'
    });
};

export const clientGetPayments = <ThrowOnError extends boolean = false>(options?: Options<ClientGetPaymentsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetPaymentsResponse, ClientGetPaymentsError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/payments'
    });
};

/**
 * Creates a new payment transaction based on the provided request data.
 */
export const clientCreatePayment = <ThrowOnError extends boolean = false>(options?: Options<ClientCreatePaymentData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientCreatePaymentResponse, ClientCreatePaymentError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/payments'
    });
};

/**
 * Retrieves the details of a payment by its unique identifier.
 */
export const clientGetPayment = <ThrowOnError extends boolean = false>(options: Options<ClientGetPaymentData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetPaymentResponse, ClientGetPaymentError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/payments/{id}'
    });
};

/**
 * Cancels a payment by its unique identifier.
 */
export const clientCancelPayment = <ThrowOnError extends boolean = false>(options: Options<ClientCancelPaymentData, ThrowOnError>) => {
    return (options?.client ?? client).put<ClientCancelPaymentResponse, ClientCancelPaymentError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/payments/{paymentId}/cancelled'
    });
};

/**
 * Retrieves a list with all permissions for the portal.
 */
export const getAllPermissions = <ThrowOnError extends boolean = false>(options?: Options<GetAllPermissionsData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetAllPermissionsResponse, GetAllPermissionsError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/permissions'
    });
};

/**
 * Retrieves the permissions for the current user within a specific jurisdiction context.
 */
export const clientGetPermissions = <ThrowOnError extends boolean = false>(options?: Options<ClientGetPermissionsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetPermissionsResponse, ClientGetPermissionsError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/permissions'
    });
};

/**
 * Gets a paginated list of reports by specified types.
 * Sample request:
 *
 * GET /api/v1/management/reports?PageNumber=1&PageSize=100&ReportTypes=Financial&ReportTypes=ContactsInfo
 * .
 */
export const managementGetReportsByType = <ThrowOnError extends boolean = false>(options?: Options<ManagementGetReportsByTypeData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetReportsByTypeResponse, ManagementGetReportsByTypeError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/reports'
    });
};

/**
 * Downloads a financial report as a file based on the specified reportId.
 */
export const managementDownloadReport = <ThrowOnError extends boolean = false>(options: Options<ManagementDownloadReportData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementDownloadReportResponse, ManagementDownloadReportError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/reports/{reportId}/download'
    });
};

/**
 * Gets the list of submissions with created RFI requests that match the criteria.
 * Sample request:
 *
 * GET /api/v1/management/requests-for-information/submissions.
 */
export const managementListRfiSubmissions = <ThrowOnError extends boolean = false>(options?: Options<ManagementListRfiSubmissionsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementListRfiSubmissionsResponse, ManagementListRfiSubmissionsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/requests-for-information/submissions'
    });
};

/**
 * Gets the given submission with the request for information and financial period changes details added.
 * Sample request:
 *
 * GET /api/v1/management/requests-for-information/submissions/{submissionId}/details.
 */
export const managementGetSubmissionRfiDetails = <ThrowOnError extends boolean = false>(options: Options<ManagementGetSubmissionRfiDetailsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetSubmissionRfiDetailsResponse, ManagementGetSubmissionRfiDetailsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/requests-for-information/submissions/{submissionId}/details'
    });
};

/**
 * Create a new request for information for a submission.
 * Sample request:
 *
 * POST /api/v1/management/requests-for-information.
 */
export const managementRfiCreate = <ThrowOnError extends boolean = false>(options?: Options<ManagementRfiCreateData, ThrowOnError>) => {
    return (options?.client ?? client).post<ManagementRfiCreateResponse, ManagementRfiCreateError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/requests-for-information'
    });
};

/**
 * Upload a document for a request for information.
 * Sample request:
 * POST /api/v1/management/requests-for-information/{requestForInformationId}/documents
 * {
 * data
 * }.
 */
export const managementRfiDocumentCreate = <ThrowOnError extends boolean = false>(options: Options<ManagementRfiDocumentCreateData, ThrowOnError>) => {
    return (options?.client ?? client).post<ManagementRfiDocumentCreateResponse, ManagementRfiDocumentCreateError, ThrowOnError>({
        ...options,
        ...formDataBodySerializer,
        headers: {
            'Content-Type': null,
            ...options?.headers
        },
        url: '/api/v1/management/requests-for-information/{requestForInformationId}/documents'
    });
};

/**
 * Cancels the submission.
 * This will mark the submission as cancelled.
 *
 * Sample request:
 *
 * PUT /api/v1/management/requests-for-information/{requestForInformationId}/cancelled
 * {
 *
 * }.
 */
export const managementCancelRfi = <ThrowOnError extends boolean = false>(options: Options<ManagementCancelRfiData, ThrowOnError>) => {
    return (options?.client ?? client).put<ManagementCancelRfiResponse, ManagementCancelRfiError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/requests-for-information/{requestForInformationId}/cancelled'
    });
};

/**
 * Gets the given submission with the request for information and financial period changes details added.
 * Sample request:
 *
 * GET /api/v1/client/requests-for-information/details?submissionId={submissionId}.
 */
export const clientGetSubmissionRfiDetails = <ThrowOnError extends boolean = false>(options?: Options<ClientGetSubmissionRfiDetailsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetSubmissionRfiDetailsResponse, ClientGetSubmissionRfiDetailsError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/requests-for-information/details'
    });
};

/**
 * Complete a request for information.
 * Sample request:
 *
 * PUT /api/v1/management/requests-for-information/{requestForInformationId}/completed.
 */
export const clientRfiCcomplete = <ThrowOnError extends boolean = false>(options: Options<ClientRfiCcompleteData, ThrowOnError>) => {
    return (options?.client ?? client).put<ClientRfiCcompleteResponse, ClientRfiCcompleteError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/requests-for-information/{requestForInformationId}/completed'
    });
};

/**
 * Upload a document for a request for information.
 * Sample request:
 * POST /api/v1/client/requests-for-information/{requestForInformationId}/documents
 * {
 * data
 * }.
 */
export const clientRfiDocumentCreate = <ThrowOnError extends boolean = false>(options: Options<ClientRfiDocumentCreateData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientRfiDocumentCreateResponse, ClientRfiDocumentCreateError, ThrowOnError>({
        ...options,
        ...formDataBodySerializer,
        headers: {
            'Content-Type': null,
            ...options?.headers
        },
        url: '/api/v1/client/requests-for-information/{requestForInformationId}/documents'
    });
};

/**
 * Gets the given Shareholder.
 * Sample request:
 *
 * GET /api/v1/client/shareholders/{relationId}
 */
export const clientGetShareholder = <ThrowOnError extends boolean = false>(options: Options<ClientGetShareholderData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetShareholderResponse, ClientGetShareholderError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/shareholders/{relationId}'
    });
};

/**
 * Puts a confirmation for the Shareholder data.
 * Sample request:
 *
 * PUT /api/v1/client/shareholders/{relationId}/confirmation
 */
export const clientConfirmShareholder = <ThrowOnError extends boolean = false>(options: Options<ClientConfirmShareholderData, ThrowOnError>) => {
    return (options?.client ?? client).put<ClientConfirmShareholderResponse, ClientConfirmShareholderError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/shareholders/{relationId}/confirmation'
    });
};

/**
 * Posts an update request for the Shareholder.
 * Sample request:
 *
 * POST /api/v1/client/shareholders/{relationId}/update-request
 * {
 * requestUpdateDTO
 * }
 *
 * UpdateRequestType is one of the following:
 *
 * MissingShareholders = 103
 * ChangeOfShareholders = 221
 * ChangeOfShareholdersAddress = 222
 * ChangeOfShareholdersParticulars = 223
 * OtherUpdateOfShareholders = 303.
 */
export const clientRequestShareholderUpdate = <ThrowOnError extends boolean = false>(options: Options<ClientRequestShareholderUpdateData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientRequestShareholderUpdateResponse, ClientRequestShareholderUpdateError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/shareholders/{relationId}/update-request'
    });
};

/**
 * Gets the ViewPoint sync status including details of the last successful sync.
 * Sample request:
 * GET /api/v1/management/status/viewpoint-sync.
 */
export const managementGetViewPointSyncStatus = <ThrowOnError extends boolean = false>(options?: Options<ManagementGetViewPointSyncStatusData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetViewPointSyncStatusResponse, ManagementGetViewPointSyncStatusError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/status/viewpoint-sync'
    });
};

/**
 * Gets the application version.
 * Sample request:
 * GET /api/v1/management/status/version.
 */
export const managementGetAppVersion = <ThrowOnError extends boolean = false>(options?: Options<ManagementGetAppVersionData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetAppVersionResponse, ManagementGetAppVersionError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/status/version'
    });
};

/**
 * Updates the payment status of multiple submissions.
 * Sample request:
 *
 * PUT /api/v1/management/submissions/payment-status
 * {
 * "submissionIds": ["guid1", "guid2", "guid3"],
 * "isPaid": true
 * }
 * .
 */
export const managementMarkAsPaid = <ThrowOnError extends boolean = false>(options?: Options<ManagementMarkAsPaidData, ThrowOnError>) => {
    return (options?.client ?? client).put<ManagementMarkAsPaidResponse, ManagementMarkAsPaidError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/submissions/payment-status'
    });
};

/**
 * Updates the payment status of multiple submissions based on company code and filing year.
 * Sample request:
 *
 * PUT /api/v1/management/submissions/payment-status-by-company-year
 * {
 * "moduleId": "guid",
 * "companyFilingYears": [
 * { "CompanyVPCode": "COMP1", "FinancialYear": 2021 },
 * { "CompanyVPCode": "COMP2", "FinancialYear": 2022 }
 * ],
 * "isPaid": true
 * }
 * .
 */
export const managementMarkAsPaidByCompanyAndYear = <ThrowOnError extends boolean = false>(options?: Options<ManagementMarkAsPaidByCompanyAndYearData, ThrowOnError>) => {
    return (options?.client ?? client).put<ManagementMarkAsPaidByCompanyAndYearResponse, ManagementMarkAsPaidByCompanyAndYearError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/submissions/payment-status-by-company-year'
    });
};

/**
 * Gets the payment status of submissions based on company code and filing year.
 * Sample request:
 *
 * POST /api/v1/management/submissions/payment-status-by-company-year
 * {
 * "moduleId": "guid",
 * "companyFilingYears": [
 * { "CompanyVPCode": "COMP1", "FinancialYear": 2021 },
 * { "CompanyVPCode": "COMP2", "FinancialYear": 2022 }
 * ]
 * }
 * .
 */
export const managementGetPaidStatusByCompanyAndYear = <ThrowOnError extends boolean = false>(options?: Options<ManagementGetPaidStatusByCompanyAndYearData, ThrowOnError>) => {
    return (options?.client ?? client).post<ManagementGetPaidStatusByCompanyAndYearResponse, ManagementGetPaidStatusByCompanyAndYearError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/submissions/payment-status-by-company-year'
    });
};

/**
 * Gets the given submission with optionally the FormDocument data.
 * Sample request:
 *
 * GET /api/v1/management/submissions/{submissionId}?includeFormDocument=true
 * .
 */
export const managementGetSubmission = <ThrowOnError extends boolean = false>(options: Options<ManagementGetSubmissionData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetSubmissionResponse, ManagementGetSubmissionError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/submissions/{submissionId}'
    });
};

/**
 * Create a new revision of the submission (re-opening it).
 * This will create a new revision based on the last finalized revision.
 *
 * Sample request:
 *
 * POST /api/v1/management/submissions/{submissionId}/reopen-request
 * {
 * Comments: "Comment 123"
 * }.
 */
export const managementReopenSubmission = <ThrowOnError extends boolean = false>(options: Options<ManagementReopenSubmissionData, ThrowOnError>) => {
    return (options?.client ?? client).post<ManagementReopenSubmissionResponse, ManagementReopenSubmissionError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/submissions/{submissionId}/reopen-request'
    });
};

/**
 * Gets the list of submissions that match the criteria.
 * Use the GeneralSearchTerm to search in either entity name, masterclient code or referral office (OR)\
 * or use the specific searchterms to search for a combination (AND).
 *
 * Sample request:
 *
 * GET /api/v1/management/submissions.
 */
export const managementListSubmissions = <ThrowOnError extends boolean = false>(options?: Options<ManagementListSubmissionsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementListSubmissionsResponse, ManagementListSubmissionsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/submissions'
    });
};

/**
 * Generate a submission report given a module and a filter request.
 * Use the GeneralSearchTerm to search in either entity name, masterclient code or referral office.
 *
 * Sample request:
 *
 * GET /api/v1/management/submissions/report.
 */
export const managementGenerateNevisSubmissionsReport = <ThrowOnError extends boolean = false>(options?: Options<ManagementGenerateNevisSubmissionsReportData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGenerateNevisSubmissionsReportResponse, ManagementGenerateNevisSubmissionsReportError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/submissions/report'
    });
};

/**
 * Exports the submissions specified in the request for the ITA.
 * Sample request:
 *
 * PUT /api/v1/management/submissions/export.
 */
export const managementExportSubmission = <ThrowOnError extends boolean = false>(options?: Options<ManagementExportSubmissionData, ThrowOnError>) => {
    return (options?.client ?? client).put<ManagementExportSubmissionResponse, ManagementExportSubmissionError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/submissions/export'
    });
};

/**
 * Generate a zip file containing the submission documents.
 * Sample request:
 *
 * GET /api/v1/management/submissions/{submissionId}/documents.
 */
export const managementDownloadSubmissionDocuments = <ThrowOnError extends boolean = false>(options: Options<ManagementDownloadSubmissionDocumentsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementDownloadSubmissionDocumentsResponse, ManagementDownloadSubmissionDocumentsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/submissions/{submissionId}/documents'
    });
};

/**
 * Generate a zip file containing the submission documents.
 * Sample request:
 *
 * GET /api/v1/management/submissions/documents.
 */
export const managementDownloadSubmissionsDocuments = <ThrowOnError extends boolean = false>(options?: Options<ManagementDownloadSubmissionsDocumentsData, ThrowOnError>) => {
    return (options?.client ?? client).put<ManagementDownloadSubmissionsDocumentsResponse, ManagementDownloadSubmissionsDocumentsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/submissions/documents'
    });
};

/**
 * Updates the submission general information.
 * This update basic information fo a submission.
 *
 * Sample request:
 *
 * PUT /api/v1/management/submissions/{submissionId}/information
 * {
 * data
 * }.
 */
export const managementUpdateSubmissionInformation = <ThrowOnError extends boolean = false>(options: Options<ManagementUpdateSubmissionInformationData, ThrowOnError>) => {
    return (options?.client ?? client).put<ManagementUpdateSubmissionInformationResponse, ManagementUpdateSubmissionInformationError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/submissions/{submissionId}/information'
    });
};

/**
 * Gets the list of submissions that match the criteria.
 * Use the GeneralSearchTerm to search in either entity name, masterclient code or referral office.
 *
 * Sample request:
 *
 * GET /api/v1/management/panama/submissions.
 */
export const managementPanamaListSubmissionsByModule = <ThrowOnError extends boolean = false>(options?: Options<ManagementPanamaListSubmissionsByModuleData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementPanamaListSubmissionsByModuleResponse, ManagementPanamaListSubmissionsByModuleError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/panama/submissions'
    });
};

/**
 * Generate a submission report given a module and a filter request.
 * Use the GeneralSearchTerm to search in either entity name, masterclient code or referral office.
 *
 * Sample request:
 *
 * GET /api/v1/management/panama/submissions.
 */
export const managementGenerateSubmissionsReport = <ThrowOnError extends boolean = false>(options?: Options<ManagementGenerateSubmissionsReportData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGenerateSubmissionsReportResponse, ManagementGenerateSubmissionsReportError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/panama/submissions/report'
    });
};

/**
 * Exports the submission data specified in the request (Submission data).
 */
export const managementExportSubmissionData = <ThrowOnError extends boolean = false>(options?: Options<ManagementExportSubmissionDataData, ThrowOnError>) => {
    return (options?.client ?? client).put<ManagementExportSubmissionDataResponse, ManagementExportSubmissionDataError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/panama/submissions/submission-data/export'
    });
};

/**
 * Gets the list of submissions that match the criteria.
 * Use the GeneralSearchTerm to search in either entity name, masterclient code or referral office.
 *
 * Sample request:
 *
 * GET /api/v1/management/bahamas/submissions.
 */
export const managementBahamasListSubmissionsByModule = <ThrowOnError extends boolean = false>(options?: Options<ManagementBahamasListSubmissionsByModuleData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementBahamasListSubmissionsByModuleResponse, ManagementBahamasListSubmissionsByModuleError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/bahamas/submissions'
    });
};

/**
 * Generate a submission report given a module and a filter request.
 * Sample request:
 *
 * GET /api/v1/management/bahamas/submissions/report.
 */
export const managementBahamasGenerateSubmissionsReport = <ThrowOnError extends boolean = false>(options?: Options<ManagementBahamasGenerateSubmissionsReportData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementBahamasGenerateSubmissionsReportResponse, ManagementBahamasGenerateSubmissionsReportError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/bahamas/submissions/report'
    });
};

/**
 * Gets the given submission with optionally the FormDocument data.
 * Sample request:
 *
 * GET /api/v1/client/submissions/{submissionId}?includeFormDocument=true.
 */
export const clientGetSubmission = <ThrowOnError extends boolean = false>(options: Options<ClientGetSubmissionData, ThrowOnError>) => {
    return (options?.client ?? client).get<ClientGetSubmissionResponse, ClientGetSubmissionError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/submissions/{submissionId}'
    });
};

/**
 * Deletes the submission.
 * This will mark the submission as deleted.
 *
 * Sample request:
 *
 * DELETE /api/v1/client/submissions/{submissionId}
 * {
 *
 * }.
 */
export const clientDeleteSubmission = <ThrowOnError extends boolean = false>(options: Options<ClientDeleteSubmissionData, ThrowOnError>) => {
    return (options?.client ?? client).delete<ClientDeleteSubmissionResponse, ClientDeleteSubmissionError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/submissions/{submissionId}'
    });
};

/**
 * Updates the dataset in the given submission.
 * This will update the latest revision.
 *
 * Sample request:
 *
 * PUT /api/v1/client/submissions/{submissionId}/dataset
 * {
 *
 * }.
 */
export const clientPutSubmissionDataSet = <ThrowOnError extends boolean = false>(options: Options<ClientPutSubmissionDataSetData, ThrowOnError>) => {
    return (options?.client ?? client).put<ClientPutSubmissionDataSetResponse, ClientPutSubmissionDataSetError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/submissions/{submissionId}/dataset'
    });
};

/**
 * Finalizes/submits the submission.
 * This will finalize the submisison, the document and the current revision.
 *
 * Sample request:
 *
 * POST /api/v1/client/submissions/{submissionId}/finalize-request
 * {
 *
 * }.
 */
export const clientSubmitSubmission = <ThrowOnError extends boolean = false>(options: Options<ClientSubmitSubmissionData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientSubmitSubmissionResponse, ClientSubmitSubmissionError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/submissions/{submissionId}/finalize-request'
    });
};

/**
 * Updates the submission general information.
 * This update basic information fo a submission.
 *
 * Sample request:
 *
 * PUT /api/v1/client/submissions/{submissionId}/information
 * {
 * data
 * }.
 */
export const clientUpdateSubmissionInformation = <ThrowOnError extends boolean = false>(options: Options<ClientUpdateSubmissionInformationData, ThrowOnError>) => {
    return (options?.client ?? client).put<ClientUpdateSubmissionInformationResponse, ClientUpdateSubmissionInformationError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/submissions/{submissionId}/information'
    });
};

/**
 * Creates a new payment transaction.
 * Sample request:
 *
 * POST /api/v1/client/payments/{paymentId}/transactions
 * {
 * "FirstName": "Juan",
 * "LastName": "Hey",
 * "Email": "<EMAIL>",
 * "Description": "Small Order",
 * "OrderId": "1234",
 * "PaymentRedirectUrl": "https://payments.com/form",
 * "CancelUrl": "https://example.com/cancel",
 * "CompanyName": "Demo Inc",
 * "PhoneNumber": "+**********",
 * "MerchantEmail": "<EMAIL>"
 * }
 *
 * Sample response:
 *
 * {
 * "paymentProcessorResponse": {
 * "transactionId": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
 * "providerTransactionId": "tx_123",
 * "callBackUrl": "https://example.com/callback",
 * "result": 0,
 * "resultText": "Approved",
 * "resultNumber": 200,
 * "provider": {
 * // Provider details
 * }
 * }
 * }
 */
export const clientCreatePaymentTransaction = <ThrowOnError extends boolean = false>(options: Options<ClientCreatePaymentTransactionData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientCreatePaymentTransactionResponse, ClientCreatePaymentTransactionError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/payments/{paymentId}/transactions'
    });
};

/**
 * Submits a payment for processing based on the provided details in the request body.
 * This endpoint processes a payment by submitting a request containing the transaction details, including the `TransactionId` and `TokenId`.
 *
 * Sample request:
 *
 * POST /api/v1/client/payments/{paymentId}/transactions/{transactionId}/submit
 * {
 * "transactionId": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
 * "tokenId": "token_abc123"
 * }
 *
 * Sample response (Status 200 OK):
 *
 * {
 * "transactionId": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
 * "providerTransactionId": "txn_987654",
 * "result": 1,
 * "resultText": "Approved",
 * "resultNumber": 1001
 * }
 */
export const clientSubmitPaymentTransaction = <ThrowOnError extends boolean = false>(options: Options<ClientSubmitPaymentTransactionData, ThrowOnError>) => {
    return (options?.client ?? client).post<ClientSubmitPaymentTransactionResponse, ClientSubmitPaymentTransactionError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/payments/{paymentId}/transactions/{transactionId}/submit'
    });
};

/**
 * Puts the signed-in status for the user with the objectid.
 * For ExternallId users provide the email address also.
 * If no email address, the user is assumed to be an Entra user.
 *
 * Sample request:
 *
 * PUT  /api/v1/security/users/signed-in?email={email}&objectid={objectid}.
 */
export const securitySetUserSignedIn = <ThrowOnError extends boolean = false>(options?: Options<SecuritySetUserSignedInData, ThrowOnError>) => {
    return (options?.client ?? client).put<SecuritySetUserSignedInResponse, SecuritySetUserSignedInError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/users/signed-in'
    });
};

/**
 * Puts the signed-out status for the given user.
 * Sample request:
 *
 * PUT  /api/v1/security/users/{userId}/signed-out.
 */
export const securitySetUserSignedOut = <ThrowOnError extends boolean = false>(options: Options<SecuritySetUserSignedOutData, ThrowOnError>) => {
    return (options?.client ?? client).put<SecuritySetUserSignedOutResponse, SecuritySetUserSignedOutError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/users/{userId}/signed-out'
    });
};

/**
 * Retrieves the permissions for the given user within a specific jurisdiction context (optional).
 * Sample request:
 *
 * GET  /api/v1/security/users/{userId}/permissions?jurisdictionId={jurisdictionId}
 * .
 */
export const getPermissions = <ThrowOnError extends boolean = false>(options: Options<GetPermissionsData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetPermissionsResponse, GetPermissionsError, ThrowOnError>({
        ...options,
        url: '/api/v1/security/users/{userId}/permissions'
    });
};

/**
 * Gets a paginated list of users.
 * Sample request:
 *
 * GET /api/v1/management/users
 */
export const managementGetUsers = <ThrowOnError extends boolean = false>(options?: Options<ManagementGetUsersData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetUsersResponse, ManagementGetUsersError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/users'
    });
};

/**
 * Posts a new user.
 * Sample request:
 *
 * PUT  /api/v1/management/users
 * {
 * }
 * .
 */
export const createUser = <ThrowOnError extends boolean = false>(options?: Options<CreateUserData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateUserResponse, CreateUserError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/users'
    });
};

/**
 * Retrieves the details of a user by their unique identifier.
 */
export const managementGetUser = <ThrowOnError extends boolean = false>(options: Options<ManagementGetUserData, ThrowOnError>) => {
    return (options?.client ?? client).get<ManagementGetUserResponse, ManagementGetUserError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/users/{id}'
    });
};

/**
 * Blocks or unblocks a user.
 * Sample request:
 *
 * Patch /api/v1/management/users/{userId}/block
 * {
 * "isBlocked": true
 * }.
 */
export const blockUnblockUser = <ThrowOnError extends boolean = false>(options: Options<BlockUnblockUserData, ThrowOnError>) => {
    return (options?.client ?? client).patch<BlockUnblockUserResponse, BlockUnblockUserError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/users/{userId}/block'
    });
};

/**
 * Puts the list of masterclients for the user.
 * Sample request:
 *
 * PUT  /api/v1/management/users/{userId}/masterclients
 * {
 *
 * }
 */
export const setUserMasterClients = <ThrowOnError extends boolean = false>(options: Options<SetUserMasterClientsData, ThrowOnError>) => {
    return (options?.client ?? client).put<SetUserMasterClientsResponse, SetUserMasterClientsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/users/{userId}/masterclients'
    });
};

/**
 * Posts a new invitation for the user.
 * You can pass amasterclientid to have a specific masterclientcode mentioned on the invitation.
 *
 * Sample request:
 *
 * POST  /api/v1/management/users/{userId}/invitation?masterclientid=
 * {
 *
 * }.
 */
export const sendUserInvitation = <ThrowOnError extends boolean = false>(options: Options<SendUserInvitationData, ThrowOnError>) => {
    return (options?.client ?? client).post<SendUserInvitationResponse, SendUserInvitationError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/users/{userId}/invitation'
    });
};

/**
 * Posts new invitations for the list of users (and optional mastercclients).
 * Users in UserIds will get an invitation on the first masterclient if multiple.\
 * Users in UserMasterClients will get an invitation on the specified masterclient.\
 * \
 * Sample request:\
 * POST  /api/v1/management/users/invitations\
 * {\
 * "UserIds": [\
 * "d290f1ee-6c54-4b01-90e6-d701748f0851",\
 * "c56a4180-65aa-42ec-a945-5fd21dec0538"\
 * ],\
 * "UserMasterClients": [\
 * {\
 * "UserId": "d290f1ee-6c54-4b01-90e6-d701748f0851",\
 * "MasterClientId": "5abfae2f-85e1-451b-9248-cd2e120e74ef"\
 * },\
 * {\
 * "UserId": "c56a4180-65aa-42ec-a945-5fd21dec0538",\
 * "MasterClientId": "5abfae2f-85e1-451b-9248-cd2e120e74ef"\
 * }\
 * ]\
 * }.
 */
export const sendUserInvitations = <ThrowOnError extends boolean = false>(options?: Options<SendUserInvitationsData, ThrowOnError>) => {
    return (options?.client ?? client).post<SendUserInvitationsResponse, SendUserInvitationsError, ThrowOnError>({
        ...options,
        url: '/api/v1/management/users/invitations'
    });
};

/**
 * Gets the Terms and Conditions acceptance status for a user.
 * Returns the acceptance status and timestamp of when Terms and Conditions were accepted.
 *
 * Sample request:
 *
 * GET /api/client/users/{userId}/terms-and-conditions-status.
 */
export const getTermsConditionsStatus = <ThrowOnError extends boolean = false>(options: Options<GetTermsConditionsStatusData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetTermsConditionsStatusResponse, GetTermsConditionsStatusError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/users/{userId}/terms-and-conditions-status'
    });
};

/**
 * Sets the Terms and Conditions acceptance status for a user.
 * Updates the acceptance status and records the timestamp.
 *
 * Sample request:
 *
 * PUT /api/client/users/{userId}/terms-and-conditions-status.
 */
export const acceptTermsConditions = <ThrowOnError extends boolean = false>(options: Options<AcceptTermsConditionsData, ThrowOnError>) => {
    return (options?.client ?? client).put<AcceptTermsConditionsResponse, AcceptTermsConditionsError, ThrowOnError>({
        ...options,
        url: '/api/v1/client/users/{userId}/terms-and-conditions-status'
    });
};