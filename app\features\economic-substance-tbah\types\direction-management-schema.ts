import { z } from "zod";
import { nonEmptyString, preprocessArray, stringBoolean, stringNumber } from "~/lib/utilities/zod-validators";

export const directorSchema = z.object({
  name: nonEmptyString("Name"),
  isResidentInBahamas: stringBoolean(),
  relationToEntity: nonEmptyString("Relation to the Entity"),
  meetingsAttended: stringBoolean(),
  meetingNumber: z.array(z.string()).default([]).optional(),
  physicallyPresentInBahamas: stringBoolean().optional(),
  qualification: z.string().optional(),
  yearsOfExperience: z.string().optional(),
})
  .superRefine((data, ctx) => {
    //  If meetingsAttended is "true", validate additional rules
    if (data.meetingsAttended === "true") {
      // meetingNumber is required
      if (!data.meetingNumber || data.meetingNumber.length === 0) {
        ctx.addIssue({
          code: "custom",
          path: ["meetingNumber"],
          message: "Required",
        });
      }

      // physicallyPresentInBahamas is required
      if (!data.physicallyPresentInBahamas) {
        ctx.addIssue({
          code: "custom",
          path: ["physicallyPresentInBahamas"],
          message: "Required",
        });
      }

      // qualification is required
      if (!data.qualification) {
        ctx.addIssue({
          code: "custom",
          path: ["qualification"],
          message: "Required",
        });
      }

      // qualification is required
      if (!data.yearsOfExperience) {
        ctx.addIssue({
          code: "custom",
          path: ["yearsOfExperience"],
          message: "Required",
        });
      }
    }
  });

export type DirectorSchemaType = z.infer<typeof directorSchema>

export const directionManagementSchema = z.object({
  isDirectedAndManagedInBahamas: stringBoolean(),
  numberOfMeetings: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: -1, lessThan: 999, allowDecimal: true }),
  numberOfMeetingsInBahamas: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: -1, lessThan: 999, allowDecimal: true }),
  quorumDirectors: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: -1, lessThan: 999, allowDecimal: true }),
  quorumPhysicallyPresent: stringBoolean(),
  areMinutesKeptInBahamas: stringBoolean(),
  directors: preprocessArray(z.array(directorSchema)),
})
  .refine(data => !(data.quorumPhysicallyPresent === "false" && (data.directors.length < 1)), {
    message: "At least 1 director is required",
    path: ["directors", 0],
  })
  .refine(data => !(data.quorumPhysicallyPresent === "true" && (data.directors.length < Number(data.quorumDirectors))), {
    message: "The number of directors should be the same as the number of quorum for board meetings",
    path: ["directors", 0],
  })

export type DirectionManagementSchemaType = z.infer<typeof directionManagementSchema>

export function getDirectionManagementDefaultValues(data: DirectionManagementSchemaType | undefined): DirectionManagementSchemaType {
  return {
    isDirectedAndManagedInBahamas: (data?.isDirectedAndManagedInBahamas ?? undefined) as "true" | "false", // bypass to set as undefined the first status
    numberOfMeetings: data?.numberOfMeetings ?? "",
    numberOfMeetingsInBahamas: data?.numberOfMeetingsInBahamas ?? "",
    quorumDirectors: data?.quorumDirectors ?? "",
    quorumPhysicallyPresent: (data?.quorumPhysicallyPresent ?? undefined) as "true" | "false",
    areMinutesKeptInBahamas: (data?.areMinutesKeptInBahamas ?? undefined) as "true" | "false",
    directors: data?.directors ?? [],
  };
}
