import type { NotificationProps } from "@netpro/design-system";
import type { Session, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/node";
import { redirect } from "@remix-run/react";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";

export async function flashNotification(request: Request, args: NotificationProps): Promise<Session> {
  const session = await getSession(request.headers.get("Cookie"));
  session.flash("notification", args);

  return session;
}

/**
 * When all you want to do is return a flash notification as JSON response, use this neat little
 * helper function like this:
 *
 * ```ts
 * import { jsonFlashNotification } from "~/lib/utilities/flash-notification";
 *
 * export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse | null> {
 *  const formData = await request.formData();
 *  if (formData.get("action") === "_invoice") {
 *    return jsonFlashNotification(request, {
 *      variant: "warning",
 *      title: "Payments module required",
 *      message: "Invoices not ready yet.",
 *    })
 *  }
 *  return null;
 *  }
 *  ```
 */
export async function jsonFlashNotification<T>(request: Request, args: NotificationProps, data: T | null = null): Promise<TypedResponse> {
  const session = await flashNotification(request, args);

  return json(data, {
    headers: {
      "Set-Cookie": await commitSession(session),
    },
  });
}

export async function redirectWithFlashNotification(
  request: Request,
  args: NotificationProps & { location: string },
): Promise<TypedResponse> {
  const session = await flashNotification(request, args);

  return redirect(args.location, {
    headers: {
      "Set-Cookie": await commitSession(session),
    },
  })
}
