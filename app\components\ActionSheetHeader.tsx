import type { FC, PropsWithChildren } from "react";

export type ActionSheetHeaderProps = {
  subText?: string
} & PropsWithChildren

export const ActionSheetHeader: FC<ActionSheetHeaderProps> = ({ children, subText }) => {
  return (
    <div className="mb-2 text-blue-700 flex flex-col">
      <h4 className="text-xl font-semibold">{children}</h4>
      {subText && <span className="text-xs text-blue-700 mb-3">{subText}</span>}
    </div>
  )
}
