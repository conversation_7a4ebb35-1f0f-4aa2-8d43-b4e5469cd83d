import type { To } from "@remix-run/router";
import type { NavigateFunction, NavigateOptions } from "react-router";
import { useNavigate } from "@remix-run/react";
import { useCallback } from "react";

export function useDelayedNavigate({
  delay = 200,
  beforeDelay = (): void => {},
  afterDelay = (): void => {},
} = {}): (toOrDelta: To | number, options?: NavigateOptions) => void {
  const navigate: NavigateFunction = useNavigate();

  return useCallback(
    (toOrDelta: To | number, options?: NavigateOptions): void => {
      beforeDelay();
      setTimeout(() => {
        if (typeof toOrDelta === "number") {
          // Handle delta (e.g., history navigation)
          navigate(toOrDelta);
        } else {
          // Handle 'to' with options
          navigate(toOrDelta, options);
        }

        afterDelay();
      }, delay);
    },
    [delay, navigate, beforeDelay, afterDelay],
  );
}
