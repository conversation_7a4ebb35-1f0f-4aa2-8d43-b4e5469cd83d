import type { DisplayColumnDef } from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns";
import type { ListAnnouncementDTO } from "~/services/api-generated";

const columnHelper = createColumnHelper<ListAnnouncementDTO>();

export const announcementColumns: DisplayColumnDef<ListAnnouncementDTO>[] = [
  columnHelper.display({
    id: "sendAt",
    header: "Scheduled Date",
    cell: props => props.row.original.sendAt && format(props.row.original.sendAt, "yyyy-MM-dd"),
  }),
  columnHelper.display({
    id: "subject",
    header: "Subject",
    cell: props => props.row.original.subject,
  }),
  columnHelper.display({
    id: "masterClients",
    header: "Master Clients",
    cell: props => props.row.original.masterClients?.map(masterClient => (
      <div key={masterClient.id}>
        {masterClient.code}
      </div>
    )),
  }),
  columnHelper.display({
    id: "jurisdictions",
    header: "Jurisdiction",
    cell: props => props.row.original.jurisdictions?.map(jurisdiction => (
      <div key={jurisdiction.id}>
        {jurisdiction.name}
      </div>
    )),
  }),
  columnHelper.display({
    id: "status",
    header: "Status",
    cell: props => props.row.original.status,
  }),
]
