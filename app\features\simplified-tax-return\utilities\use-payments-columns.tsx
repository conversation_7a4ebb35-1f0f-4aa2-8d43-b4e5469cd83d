import type { DisplayColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@netpro/design-system";
import { createColumnHelper } from "@tanstack/react-table";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import type { ListSubmissionDTO } from "~/services/api-generated";

const columnHelper = createColumnHelper<ListSubmissionDTO>();

export function usePaymentsColumns() {
  const formatColDate = useFormatColDate()
  //
  const paymentsColumns: DisplayColumnDef<ListSubmissionDTO>[] = [
    columnHelper.display({
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected()
            || (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={value => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
    }),
    columnHelper.display({
      id: "companyName",
      header: "Entity Name",
      cell: props => props.row.original.legalEntityName,
    }),
    columnHelper.display({
      id: "legalEntityCode",
      header: "Regulatory Code",
      cell: props => props.row.original.legalEntityCode,
    }),
    columnHelper.display({
      id: "legalEntityVPCode",
      header: "VP Code",
      cell: props => props.row.original.legalEntityVPCode,
    }),
    columnHelper.display({
      id: "masterClientCode",
      header: "Master Client Code",
      cell: props => props.row.original.masterClientCode,
    }),
    columnHelper.display({
      id: "isPaid",
      header: "Status",
      cell: props => props.row.original.isPaid ? "Paid" : "Unpaid",
    }),
    columnHelper.display({
      id: "txId",
      header: "TX ID",
      cell: props => props.row.original.txId,
    }),
    columnHelper.display({
      id: "createdAt",
      header: "Date Created",
      cell: formatColDate("createdAt"),
    }),
    columnHelper.display({
      id: "submittedAt",
      header: "Submitted Date",
      cell: formatColDate("submittedAt"),
    }),
    columnHelper.display({
      id: "financialYear",
      header: "Financial Year",
      cell: props => props.row.original.financialYear,
    }),
  ]

  return paymentsColumns
}
