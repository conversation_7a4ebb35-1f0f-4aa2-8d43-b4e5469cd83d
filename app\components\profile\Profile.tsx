import type { ReactNode } from "react";
import { useUser } from "~/lib/hooks/useUser";

export function Profile(): ReactNode {
  const { name, email } = useUser();

  return (
    <div className="flex justify-start flex-col gap-0.5">
      <span className="font-semibold">{name}</span>
      <span className="text-sm text-gray-500 font-semibold truncate" title={email}>
        {email}
      </span>
    </div>
  );
}
