import { z } from "zod";
import { formatDate } from "~/lib/utilities/format";

function preprocessDate(value: unknown): string | undefined {
  if (typeof value === "string" && !Number.isNaN(Date.parse(value))) {
    return formatDate(new Date(value)); // Valid date, format it
  }

  if (value instanceof Date && !Number.isNaN(value.getTime())) {
    return formatDate(new Date(value)); // Valid date, format it
  }

  return undefined;
}

export const searchSchema = z.object({
  search: z.string().optional(),
  submittedAfter: z.preprocess(preprocessDate, z.string().optional()),
  submittedBefore: z.preprocess(preprocessDate, z.string().optional()),
});

export type SearchSchema = z.infer<typeof searchSchema>
