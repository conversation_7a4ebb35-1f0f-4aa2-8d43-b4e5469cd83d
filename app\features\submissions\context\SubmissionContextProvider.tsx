import { type ReactNode, useMemo } from "react";
import { SubmissionContext, type SubmissionContextType } from "./submission-context";

export function SubmissionProvider({ submissionData, financialYear, children }: SubmissionContextType & { children: ReactNode }): ReactNode {
  const value = useMemo(() => ({
    submissionData,
    financialYear,
  }), [submissionData, financialYear]);

  return (
    <SubmissionContext.Provider value={value}>
      {children}
    </SubmissionContext.Provider>
  );
}
