import type { ZodTypeAny } from "zod";
import { z } from "zod";

export function phoneSchema({
  required = false,
  inputName = "Phone number",
}: {
  required?: boolean
  inputName?: string
}): z.ZodEffects<ZodTypeAny> {
  return z.object({
    countryCode: z.string().optional(),
    prefix: z.string().optional(),
    number: z.string().optional(),
  })
    .refine((val) => {
      if (required) {
        return val?.countryCode && val?.prefix && val?.number;
      }

      return true;
    }, {
      message: `${inputName} is required.`,
      path: [""],
    })
    .refine(val => !(val?.countryCode && !val.number && !required), {
      message: "Please add a number or unselect prefix.",
      path: [""],
    })
    .refine(val => !(val?.number && !val.prefix && !required), {
      message: "Please select a prefix or remove number.",
      path: [""],
    });
}
