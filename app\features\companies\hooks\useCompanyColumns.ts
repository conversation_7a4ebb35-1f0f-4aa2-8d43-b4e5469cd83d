import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import { makeMakeColumn } from "~/lib/makeMakeColumn";
import type { CompanyDTO } from "~/services/api-generated";

type Type = "overview" | "pending-onboardings"

/*
 * defined in a comment by <PERSON> (<PERSON>) Heesbeen:
 * https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_sprints/taskboard/Trident%20Trust%20-%20Private%20Client%20Portal%20Team/Trident%20Trust%20-%20Private%20Client%20Portal/Iteration%2016?workitem=15259
 */
const sortableColumns = [
  "name",
  "code",
  "legacyCode",
  "incorporationNumber",
  "incorporationDate",
  "vpEntityStatus",
  "entityType",
  "referralOffice",
  "isActive",
  "onboardingStatus",
  "masterClientCode",
] as const

/**
 * This is a helper hook for the table column definitions. Currently, the definitions are shared between
 * the overview and pending onboardings pages. However, using this hook, we're able to easily change columns
 * between these views in the future.
 * @param type
 */

export function useCompanyColumns(type: Type) {
  const formatColDate = useFormatColDate()
  const makeColumn = makeMakeColumn<typeof sortableColumns[number], CompanyDTO>(sortableColumns as any)
  let columns = [];

  switch (type) {
    case "overview":
    case "pending-onboardings":
    default:
      columns = [
        makeColumn({ header: "Entity Name", id: "name", accessorKey: "name" }),
        makeColumn({ header: "Jurisdiction", id: "jurisdictionName", accessorKey: "jurisdictionName" }),
        makeColumn({ header: "Entity Type", id: "entityType", accessorKey: "entityType" }),
        makeColumn({ header: "VP Code", id: "code", accessorKey: "code" }),
        makeColumn({ header: "VP Entity Status", id: "vpEntityStatus", accessorKey: "vpEntityStatus" }),
        makeColumn({
          id: "incorporationDate",
          header: "Incorporation Date",
          accessorFn: formatColDate("incorporationDate", { fallback: "N/A" }),
        }),
        makeColumn({ header: "Incorporation Code", id: "incorporationNumber", accessorKey: "incorporationNumber" }),
        makeColumn({ header: "Regulatory Code", id: "legacyCode", accessorKey: "legacyCode" }),
        makeColumn({ header: "Master Client Code", id: "masterClientCode", accessorKey: "masterClientCode" }),
        makeColumn({ header: "PCP Entity Status", id: "isActive", cell: props => props.row.original.isActive ? "Active" : "Inactive" }),
        makeColumn({
          header: "Onboarding Status",
          id: "onboardingStatus",
          accessorKey: "onboardingStatus",
        }),
        makeColumn({ header: "Referral Office", id: "referralOffice", accessorKey: "referralOffice" }),
      ];
  }

  return { columns }
}
