import type { <PERSON> } from "react";
import { type <PERSON>tonProps, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@netpro/design-system";

type Props = {
  isLoading?: boolean
} & ButtonProps

export const Button: FC<Props> = ({ children, isLoading, ...rest }) => {
  return (
    <NetproButton {...rest} disabled={isLoading}>
      {isLoading && <Spinner className="col-span-1 size-4 mx-0 text-white" />}
      {!isLoading && children }
    </NetproButton>
  )
}
