import { companyFilingYearsSchemaServer } from "~/features/simplified-tax-return/schemas/companyFilingYearsSchema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { getJurisdictionModules, getJurisdictions, managementGetPaidStatusByCompanyAndYear } from "~/services/api-generated";

export const action = makeEnhancedAction(async ({ json, request }) => {
  const formData = await request.formData()
  const bodyData = formData.get("data") as string
  //
  const { companyFilingYears } = companyFilingYearsSchemaServer.parse(JSON.parse(bodyData))
  //
  const nevisId = await getJurisdictions({ headers: await authHeaders(request) }).then(({ data }) => data?.data?.find(({ code }) => code === "Nevis")?.id)
  if (!nevisId) {
    throw new Error("Jurisdiction not found")
  }

  const strId = await getJurisdictionModules({ headers: await authHeaders(request), path: { jurisdictionId: nevisId } }).then(({ data }) => data?.modules?.find(({ key }) => key === "SimplifiedTaxReturn")?.id)
  //
  const { data } = await managementGetPaidStatusByCompanyAndYear({ headers: await authHeaders(request), body: { companyFilingYears, moduleId: strId } });

  return json(data);
}, { authorize: ["str.payments.import"] });
