import type { ReactNode } from "react";
import { But<PERSON> } from "@netpro/design-system";
import { Link } from "@remix-run/react";
import { FileDown } from "lucide-react";
import { useFormatDate } from "~/lib/hooks/useFormatDate";
import type { ReportDTO } from "~/services/api-generated";

type Props = {
  report: ReportDTO
}

export function FinancialReportListItem({ report }: Props): ReactNode {
  const formatDate = useFormatDate()

  return (
    <div
      className="w-full p-2 bg-white rounded-lg transition-colors duration-150 border-2 border-transparent group"
    >
      <div className="flex justify-between items-center">
        <div className="flex-grow p-2 items-center justify-center">
          <div className="flex gap-1 items-start">
            <h2 className="text-xl font-bold transition-colors duration-150 group-hover:text-blue-700">{report.filename !== "" ? report.filename : "Undefined file name"}</h2>
          </div>
          <div>
            <span className="text-sm text-gray-600 transition-colors duration-150 group-hover:text-blue-800">Created at: </span>
            <span className="text-sm text-gray-600 font-semibold transition-colors duration-150">
              <span>{report.createdAt ? formatDate(report.createdAt, { extend: "HH:mm" }) : "Undefined creation date"}</span>
            </span>
          </div>
        </div>
        <div className="flex flex-row gap-1 items-center justify-center">
          <Button size="sm" variant="outline" className="self-center text-sm flex items-center justify-center gap-1.5 mr-2" asChild>
            <Link to={`/downloads/financial-reports/${report.id}`} reloadDocument>
              <FileDown className="w-4 h-4 text-blue-600" />
              <span className="text-xs font-semibold">Download</span>
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
