import queryString from "query-string";
import { z } from "zod";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementDownloadSubmissionDocuments } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ params, request, enhancedURL, setNotification, redirect }) => {
  await middleware(["auth"], request);
  const { id } = params

  if (!id) {
    throw new Response("The Id is required", { status: 400 })
  }

  const searchParams = new URLSearchParams(enhancedURL.search);
  const data = queryString.parse(searchParams.toString(), { arrayFormat: "bracket" })
  const submissionIdType = z.object({
    location: z.string(),
  });
  const { location } = submissionIdType.parse(data);
  const { data: fileData, error, response } = await managementDownloadSubmissionDocuments({ headers: await authHeaders(request), path: { submissionId: id } });

  if (error) {
    setNotification({ title: "Error!", message: error.exceptionMessage as string || undefined, variant: "error" });

    return redirect(location);
  }

  // Whitelist specific headers
  const whitelistedHeaders = new Headers();
  const allowedHeaders = ["Content-Type", "Content-Disposition"];
  for (const header of allowedHeaders) {
    const value = response.headers.get(header);
    if (value) {
      whitelistedHeaders.set(header, value);
    }
  }

  return new Response(fileData, {
    status: response.status,
    headers: whitelistedHeaders,
  });
}, { authorize: ["bfr.panama.submissions.export", "es.bahamas.submissions.export"] });
