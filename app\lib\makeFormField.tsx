import type { ComponentProps, FC, JSX } from "react";
import type { ControllerFieldState, ControllerRenderProps } from "react-hook-form";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@netpro/design-system";
import { useRemixFormContext } from "remix-hook-form";

type Args<Props extends object> = {
  displayName: string
  render: (args: {
    field: ControllerRenderProps
    fieldState: ControllerFieldState
  } & Props) => JSX.Element
}

type BaseProps = {
  name: string
  label?: string
  hideMessage?: boolean
  formItemProps?: ComponentProps<typeof FormItem>
};

/**
 * makeFormField is a helper function that creates a higher-order form component integrated with react-hook-form.
 * It handles rendering the form field, label, and validation messages, reducing boilerplate when building custom form components.
 *
 * @template Props - Additional props specific to the custom form component being created.
 *
 * @param {object} args - The arguments for creating the form field component.
 * @param {string} args.displayName - The display name for the generated component, useful for debugging and React dev tools.
 * @param {Function} args.render - A render function that receives `field`, `fieldState`, and additional props,
 * allowing customization of the rendered form element.
 *
 * @returns {FC<BaseProps & Props>} A functional component that includes form state handling and a structured layout
 * with a label and validation message.
 *
 * @example
 * // Example usage to create a custom FormDatePicker component
 * import { makeFormField } from '~/lib/makeFormField';
 * import { DatePicker } from '@netpro/design-system';
 *
 * export const FormDatePicker = makeFormField<{ datePickerProps?: ComponentProps<typeof DatePicker> }>({
 *   displayName: "FormDatePicker",
 *   render: ({ field, fieldState, datePickerProps }) => (
 *     <DatePicker
 *       date={field.value}
 *       onChange={field.onChange}
 *       invalid={!!fieldState.error}
 *       {...datePickerProps}
 *     />
 *   ),
 * });
 *
 * @example
 * // Using the generated FormDatePicker component in a form
 * import { Form } from '~/components/Form';
 * import { useFilterForm } from '~/hooks/useFilterForm';
 * import { z } from 'zod';
 * import { FormDatePicker } from '~/components/FormDatePicker';
 *
 * const schema = z.object({
 *   date: z.string().optional(),
 * });
 *
 * function FilterComponent() {
 *   const { formMethods } = useFilterForm(schema);
 *
 *   return (
 *     <Form formMethods={formMethods}>
 *       <FormDatePicker name="date" label="Select a date" />
 *     </Form>
 *   );
 * }
 */
export function makeFormField<Props extends object = object>({ displayName, render }: Args<Props>): FC<BaseProps & Props> {
  const Component: FC<BaseProps & Props> = ({ name, label, hideMessage, formItemProps, ...props },
  ) => {
    const { control } = useRemixFormContext()

    return (
      <FormField
        control={control}
        name={name}
        render={({
          field,
          fieldState,
        }) => (
          <FormItem {...formItemProps}>
            {label && <FormLabel>{label}</FormLabel>}
            <FormControl>
              {render({ field, fieldState, ...props as Props })}
            </FormControl>
            {!hideMessage && <FormMessage /> }
          </FormItem>
        )}
      />
    )
  }

  Component.displayName = displayName;

  return Component
}
