import type { ReactNode } from "react";
import { Spinner } from "@netpro/design-system";
import clsx from "clsx";

type LoadingStateProps = {
  isLoading?: boolean
  message?: string
  className?: string
  children?: ReactNode
};

export function LoadingState({
  isLoading = true,
  message = "Loading...",
  className = "",
  children,
}: LoadingStateProps): ReactNode {
  return (
    <div
      className={clsx(
        "transition-all duration-200 absolute backdrop-blur inset-0 bg-white/50 z-20 flex items-center justify-center min-h-96",
        className,
        {
          "opacity-100": isLoading,
          "opacity-0 pointer-events-none": !isLoading,
        },
      )}
    >
      {isLoading && (
        <div className="flex flex-row items-center gap-1.5">
          <Spinner className="size-5 text-blue-500" />
          <h2>{message}</h2>
        </div>
      )}
      {children}
    </div>
  );
}
