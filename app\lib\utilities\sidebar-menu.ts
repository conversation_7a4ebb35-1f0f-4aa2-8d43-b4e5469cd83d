import {
  BellRing,
  Building2,
  Home,
  Landmark,
  MonitorCog,
  Pyramid,
  ScrollText,
  User,
  UserRoundCheck,
  Wallet,
} from "lucide-react";
import type { MenuItem } from "~/components/layout/sidebar/MenuItem";

// Define the structure of the menu items
export default function menu(): MenuItem[] {
  return [
    {
      icon: Home,
      label: "Dashboard",
      href: "/dashboard",
      show: true,
    },
    {
      icon: BellRing,
      label: "Announcements",
      href: "/announcements",
      show: true,
      permissions: { startingWith: ["announcements"] },
    },
    {
      icon: Pyramid,
      label: "Master Clients",
      href: "/master-clients",
      show: true,
      permissions: { startingWith: "masterclients" },
    },
    {
      icon: User,
      label: "Users",
      href: "/users",
      show: true,
      permissions: { startingWith: "users" },
    },
    {
      icon: Building2,
      label: "Companies",
      href: "/companies",
      show: true,
      permissions: { startingWith: ["companies"] },
      children: [
        {
          label: "Overview",
          href: "/companies/overview",
          show: true,
          permissions: { oneOf: ["companies.search", "companies.view"] },
        },
        {
          label: "Pending Onboardings",
          href: "/companies/pending-onboardings",
          show: true,
          permissions: { oneOf: ["companies.onboarding.access"] },
        },
      ],
    },
    {
      icon: Landmark,
      label: "Simplified Tax Returns",
      href: "/simplified-tax-return",
      show: true,
      permissions: { startingWith: "str" },
      children: [
        {
          label: "Submissions",
          href: "/simplified-tax-return/submissions",
          show: true,
          permissions: { oneOf: ["str.submissions.view", "str.submissions.search"] },
        },
        {
          label: "IRD Export",
          href: "/simplified-tax-return/ird-export",
          show: true,
          permissions: { oneOf: ["str.submissions.export.ird"] },
        },
        {
          label: "Financial Reports",
          href: "/simplified-tax-return/financial-reports",
          show: true,
          permissions: { oneOf: ["str.invoices.export"] },
        },
        {
          label: "Payments",
          href: "/simplified-tax-return/payments",
          show: true,
          permissions: { oneOf: ["str.submissions.view-paid"] },
        },
        {
          label: "Fees",
          href: "/simplified-tax-return/fees",
          show: true,
          permissions: { oneOf: ["str.late-payments.view", "str.fee.set"] },
        },
      ],
    },
    {
      icon: Landmark,
      label: "Financial Return Management",
      href: "/financial-return",
      show: false,
      children: [
        {
          label: "Submissions",
          href: "/financial-return/submissions",
          show: false,
        },
        {
          label: "Manage requests",
          href: "/financial-return/#",
          show: false,
        },
        {
          label: "Mass-upload",
          href: "/financial-return/#",
          show: false,
        },
        {
          label: "Manage reporting",
          href: "/financial-return/#",
          show: false,
        },
      ],
    },
    {
      icon: Building2,
      label: "Economic Substance",
      href: "/economic-substance",
      show: true,
      permissions: { startingWith: "es." },
      children: [
        {
          label: "Submissions",
          href: "/economic-substance/submissions",
          show: true,
          permissions: { oneOf: ["es.bvi.submissions.view", "es.bahamas.submissions.view"] },
        },
        {
          label: "IRD Export",
          href: "/economic-substance/ird-export",
          show: true,
          permissions: { oneOf: ["es.bvi.submissions.export.ita", "es.bahamas.submissions.export.ita"] },
        },
        {
          label: "Payments",
          href: "/economic-substance/payments",
          show: true,
          permissions: { oneOf: ["es.bvi.payments.import", "es.bahamas.payments.import", "es.bahamas.submissions.view-paid"] },
        },
      ],
    },
    {
      icon: UserRoundCheck,
      label: "Ownership & Officers",
      href: "/bo-directors",
      show: true,
      permissions: { startingWith: "bo-dir" },
      children: [
        {
          label: "Overview",
          href: "/bo-directors/overview",
          show: true,
          permissions: { oneOf: ["bo-dir.view", "bo-dir.search"] },
        },
        {
          label: "History",
          href: "/bo-directors/history",
          show: false,
          permissions: { oneOf: ["bo-dir.view", "bo-dir.search"] },
        },
      ],
    },
    {
      icon: Landmark,
      label: "Basic Financial Report",
      href: "/basic-financial-report",
      show: true,
      permissions: { startingWith: "bfr" },
      children: [
        {
          label: "Submissions",
          href: "/basic-financial-report/submissions",
          show: true,
          permissions: { oneOf: ["bfr.panama.submissions.view", "bfr.panama.submissions.search"] },
        },
        {
          label: "Payments",
          href: "/basic-financial-report/payments",
          show: true,
          permissions: { oneOf: ["bfr.panama.submissions.view-paid"] },
        },
        {
          label: "Financial Reports",
          href: "/basic-financial-report/financial-reports",
          show: true,
          permissions: { oneOf: ["bfr.panama.invoices.export"] },
        },
        {
          label: "Export Submission Data",
          href: "/basic-financial-report/export-submission-data",
          show: true,
          permissions: { oneOf: ["bfr.panama.submissions.export"] },
        },
        {
          label: "Fees",
          href: "/basic-financial-report/fees",
          show: true,
          permissions: { oneOf: ["companies.custom-bfr-fee.view"] },
        },
      ],
    },
    {
      icon: Wallet,
      label: "Finance",
      href: "/finance",
      show: false,
      children: [
        {
          label: "Companies",
          href: "/finance/companies",
          show: true,
        },
        {
          label: "Export invoices",
          href: "/finance/export-invoices",
          show: true,
        },
        {
          label: "Late payments",
          href: "/finance/late-payments",
          show: true,
        },
      ],
    },
    {
      icon: ScrollText,
      label: "Reports",
      href: "/reports",
      show: true,
      // todo: actually define all the permissions
      permissions: { oneOf: ["str.management-information"] },
    },
    {
      icon: MonitorCog,
      label: "System Settings",
      href: "/system-settings",
      show: true,
      permissions: { oneOf: ["str.data-migration", "status.viewpoint-sync.view"] },
      children: [
        {
          label: "Data Migration",
          href: "/data-migration",
          show: true,
          // todo: actually define all the permissions
          permissions: { oneOf: ["str.data-migration"] },
        },
        {
          label: "Synchronization",
          href: "/synchronization",
          show: true,
          permissions: { oneOf: ["status.viewpoint-sync.view"] },
        },
      ],
    },
  ] as MenuItem[];
}
