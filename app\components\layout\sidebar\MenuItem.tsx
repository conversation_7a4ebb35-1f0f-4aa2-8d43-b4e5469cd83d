import type { ForwardRefExoticComponent, ReactNode, SVGProps } from "react";
import { cn } from "@netpro/design-system";
import { Link } from "@remix-run/react";
import { useUserHasPermission } from "~/hooks/use-user-has-permission";
import type { UserHasPermissionConfig } from "~/lib/utilities/user-has-permission";

export type MenuItem = {
  label: string
  href: string
  icon: ForwardRefExoticComponent<Omit<SVGProps<SVGSVGElement>, "ref">>
  children?: Omit<MenuItem, "icon">[]
  show: boolean
  count?: number
  permissions: UserHasPermissionConfig
};

export type MenuItemProps = {
  item: MenuItem
  active: boolean
};

export default function MenuItemComponent({ item, active }: MenuItemProps): ReactNode {
  const userHasPermission = useUserHasPermission(item.permissions || {})
  //
  if (!userHasPermission) {
    return null
  }

  return (
    <li>
      <Link
        to={item.href}
        className={cn(
          active ? "bg-gray-100" : "hover:bg-gray-100",
          "group flex w-full items-center gap-2.5 rounded-md py-2.5 px-2 text-sm font-semibold font-inter leading-5 text-gray-800",
        )}
      >
        <item.icon
          className={cn(active ? "text-primary" : "text-gray-400 group-hover:text-primary", "h-6 w-6 shrink-0")}
          aria-hidden="true"
        />
        <span className="block overflow-hidden text-ellipsis">
          {item.label}
        </span>
      </Link>
    </li>
  );
}
