import { authHeaders } from "~/lib/auth/utils/auth-headers";
import type { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementGetSubmission } from "~/services/api-generated";

type LoaderArgs = Parameters<Parameters<typeof makeEnhancedLoader>[0]>[0];

export async function getRfiLoader(route: string, {
  request,
  setNotification,
  redirect,
  json,
  params,
}: LoaderArgs) {
  await middleware(["auth"], request);
  const submissionResponse = await managementGetSubmission({ headers: await authHeaders(request), path: { submissionId: params.id! } })
  if (!submissionResponse.data) {
    setNotification({ title: "The requested submission could not be found", variant: "error" })

    return redirect(route)
  }

  return json({ submission: submissionResponse.data })
}
