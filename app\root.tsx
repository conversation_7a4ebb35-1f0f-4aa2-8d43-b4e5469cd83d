import type { HeadersFunction, LinksFunction, MetaArgs, MetaFunction } from "@remix-run/node";
import type { ContextUserValue } from "./components/ContextUser";
import { Button, NotificationProvider, notify, TooltipProvider } from "@netpro/design-system";
import {
  isRouteErrorResponse,
  Link,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useNavigate,
  useRouteError,
  useRouteLoaderData,
} from "@remix-run/react";
import { ChevronLeft, ChevronRight, LogIn } from "lucide-react";
import { type ReactNode, useEffect, useMemo } from "react";
import type { UserPreferencesValue } from "~/components/ContextUserPreferences";
import { ContextUserPreferences } from "~/components/ContextUserPreferences";
import stylesheet from "~/default.css?url";
import type { SessionData } from "~/lib/auth/types/session-type";
import { commitSession } from "~/lib/auth/utils/session.server";
import { defaultHeaders, defaultLinks, defaultMeta } from "~/lib/config";
import { userPreferences } from "~/lib/cookies.server";
import { getApiVersion, getAppVersion } from "~/lib/utilities/app-version";
import { ContextUser } from "./components/ContextUser";
import { useNonce } from "./lib/hooks/useNonce";
import { PAGINATION } from "./lib/hooks/usePaginationParams";
import { makeEnhancedLoader } from "./lib/makeEnhancedLoader.server";
import { middleware } from "./lib/middlewares.server";

export const links: LinksFunction = () => [
  ...defaultLinks,
  { rel: "stylesheet", href: stylesheet },
];

export const meta: MetaFunction = ({ error }: MetaArgs) => [
  ...defaultMeta(error ? "Error" : undefined),
];

export const headers: HeadersFunction = () => ({
  ...defaultHeaders(),
});

export const loader = makeEnhancedLoader(async ({ session, request, enhancedURL, json, getNotification, getUserPermissions }) => {
  // Get the version from environment variable
  const appVersion = getAppVersion();
  let userId = "";
  let apiVersion = "unknown";

  if (!enhancedURL.pathname.startsWith("/auth")) {
    ({ userId } = await middleware(["auth"], request));
    // Fetch API version if user is authenticated
    apiVersion = await getApiVersion(request);
  }

  const cookieHeader = request.headers.get("Cookie");
  const { userName, userEmail } = session.data as SessionData;
  const notification = getNotification();

  return json({
    appVersion,
    apiVersion,
    userId,
    notification,
    user: { name: userName ?? "Undefined", email: userEmail ?? "Undefined" },
    userPreferences: (await userPreferences.parse(cookieHeader)) || {},
    userPermissions: await getUserPermissions(),
  }, { headers: { "Set-Cookie": await commitSession(session) } });
})

export function Layout({ children }: { children: ReactNode[] }): ReactNode {
  const data = useRouteLoaderData<typeof loader>("root");
  const nonce = useNonce();

  useEffect(() => {
    if (data && data.notification) {
      notify(data.notification);
    }
  }, [data]);

  const preferencesValue = useMemo((): UserPreferencesValue => {
    return {
      tablePageSize: Number(data?.userPreferences?.tablePageSize || PAGINATION.PAGE_SIZE),
    }
  }, [data])
  //
  const userValue = useMemo((): ContextUserValue => {
    return data?.userPermissions || {}
  }, [data])

  return (
    <html lang="en" className="h-full antialiased">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body className="h-full">
        <ContextUser.Provider value={userValue}>
          <ContextUserPreferences.Provider value={preferencesValue}>
            <TooltipProvider>
              {children}
              <NotificationProvider />
            </TooltipProvider>
          </ContextUserPreferences.Provider>
        </ContextUser.Provider>
        <ScrollRestoration nonce={nonce} />
        <Scripts nonce={nonce} />
      </body>
    </html>
  );
}

export default function App(): ReactNode {
  return <Outlet />
}

export function ErrorBoundary(): ReactNode {
  const error = useRouteError();
  const navigate = useNavigate();
  let title = "Something went wrong";
  let message = "Please try again or contact support if the issue continues.";
  let actions = null;

  if (isRouteErrorResponse(error)) {
    switch (error.status) {
      case 401:
        title = "Unauthorized";
        message = "You are not authorized to view this page. Please login to continue.";
        actions = (
          <Button asChild className="gap-x-2" variant="outline">
            <Link to="/">
              <LogIn className="text-blue-600" />
              Back to Login
            </Link>
          </Button>
        )
        break;
      case 404:
        title = "Page not found";
        message = "The page you are looking for does not exist.";
        actions = (
          <>
            <Button className="gap-x-2" variant="outline" onClick={() => navigate(-1)}>
              <ChevronLeft className="text-blue-600" />
              Previous page
            </Button>
            or
            <Button asChild className="gap-x-2" variant="outline">
              <Link to="/dashboard">
                To Dashboard
                <ChevronRight className="text-blue-600" />
              </Link>
            </Button>
          </>
        )
        break;
      case 500:
        title = "Internal Server Error";
        message = "An error occurred while processing your request. Please try again later.";
        break;
    }
  }

  return (
    <div className="text-center pt-64">
      <h1 className="mt-4 text-3xl font-bold tracking-tight text-blue-800 sm:text-5xl">
        {title}
      </h1>
      {message && (
        <p className="mt-6 text-base leading-7 text-gray-600">
          {message}
        </p>
      )}
      {actions && (
        <div className="mt-10 flex items-center justify-center gap-x-3">
          {actions}
        </div>
      )}
    </div>
  )
}
