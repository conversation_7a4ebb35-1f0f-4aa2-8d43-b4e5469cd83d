import type { ReactNode } from "react";
import { <PERSON><PERSON>, But<PERSON>, Checkbox, Label, Separator, Spinner } from "@netpro/design-system";
import { EnvelopeClosedIcon } from "@radix-ui/react-icons";
import { Form, useFetcher, useParams } from "@remix-run/react";
import { useEffect, useMemo, useState } from "react";
import { useRemixForm } from "remix-hook-form";
import { Authorized } from "~/components/Authorized";
import { AccordionContent, AccordionItem, AccordionTrigger } from "~/components/ui/accordion";
import { MasterClientUserListItem } from "~/features/master-clients/MasterClientUserListItem";
import type { UserInviteSchemaType } from "~/features/master-clients/schemas/invite-users";
import type { Selectable } from "~/lib/types/helpers";
import type { ListUserDTO } from "~/services/api-generated";

type Props = {
  value: string
  users: ListUserDTO[]
}

export function AccordionMasterClientUsers({ value, users }: Props): ReactNode {
  const params = useParams();
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [userList, setUserList] = useState<(ListUserDTO & Selectable)[]>(
    users.map(user => ({ ...user, selected: false })),
  );
  const toggleSelectAll = (): void => {
    const allSelected = userList.every(user => user.selected);
    setUserList(userList.map(user => ({
      ...user,
      selected: !allSelected,
    })));
  };
  const toggleUserSelection = (userId: string): void => {
    const updatedList = userList.map(user =>
      user.id === userId ? { ...user, selected: !user.selected } : user,
    )
    setUserList(updatedList);
  };
  const fetcher = useFetcher<{
    errors: { userIds: string }
  } | {
    success: true
  }>();
  // Prepare submitData with a default empty array for userIds
  const submitData = useMemo(() => ({
    userIds: userList.filter(user => user.selected).map(user => user.id),
  }), [userList]);
  const { handleSubmit, formState: { isSubmitting }, clearErrors } = useRemixForm<UserInviteSchemaType>({
    mode: "onSubmit",
    submitConfig: {
      action: `/master-clients/${params.id}/edit/invite`,
    },
    submitData,
    fetcher,
  });

  useEffect(() => {
    if (errorMessage) {
      const errorTimer = setTimeout(() => {
        setErrorMessage(null);
      }, 3000);

      return () => clearTimeout(errorTimer);
    }

    if (successMessage) {
      const successTimer = setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);

      return () => clearTimeout(successTimer);
    }
  }, [errorMessage, successMessage]);

  useEffect(() => {
    if (fetcher.data && "errors" in fetcher.data) {
      setErrorMessage(fetcher.data.errors.userIds);
    } else if (fetcher.data) {
      setSuccessMessage("Invitations sent successfully");
      setUserList(prev => prev.map(user => ({
        ...user,
        selected: false,
      })));
    }
  }, [fetcher.data])

  return (
    <AccordionItem value={value} className="border-b-0">
      <AccordionTrigger className="text-blue-700 text-xl focus-visible:ring-0 outline-0">
        {`Master Client Users (${users.length})`}
      </AccordionTrigger>
      <AccordionContent>
        <Authorized
          oneOf={["masterclients.send-invitation"]}
          fallback={(
            <ul className="list-disc list-inside">
              {userList.map(user => (
                <li key={user.id}>{user.email}</li>
              ))}
            </ul>
          )}
        >
          <Label htmlFor="select-all-mcc-users" className="py-2 flex items-center mt-1 mb-2 ml-1.5 cursor-pointer">
            <Checkbox
              id="select-all-mcc-users"
              onCheckedChange={toggleSelectAll}
              checked={userList.every(user => user.selected)}
              className="mr-2"
            />
            {userList.every(user => user.selected) ? "Deselect all" : "Select all"}
          </Label>
          <Separator className="w-full bg-teal-100 ml-1 mb-2" />
          <ul>
            {userList.map(user => (
              <MasterClientUserListItem
                selectable
                onToggleSelect={() => toggleUserSelection(user.id as string)}
                selected={user.selected}
                label={user.email as string}
                key={user.id}
              />
            ))}
          </ul>
          <div className="flex flex-col mt-2 gap-2">
            <Separator className="w-full bg-teal-100 ml-1" />
            {successMessage && (
              <Alert title={successMessage} variant="success" />
            )}
            {(errorMessage && !isSubmitting) && (
              <Alert title={errorMessage} variant="error" />
            )}

            {userList.some(user => user.selected) && (
              <Form onSubmit={handleSubmit} method="POST" noValidate className="flex flex-col">
                <Button disabled={isSubmitting} onClick={() => clearErrors()} type="submit" size="sm" variant="outline" className="place-self-end inline-flex items-center gap-2">
                  {isSubmitting
                    ? <Spinner className="size-4 text-primary mr-0" />
                    : <EnvelopeClosedIcon className="text-primary" />}
                  {`Invite ${userList.filter(user => user.selected).length} selected ${userList.filter(user => user.selected).length === 1 ? "user" : "users"}`}
                </Button>
              </Form>
            )}
          </div>
        </Authorized>
      </AccordionContent>
    </AccordionItem>
  )
}
