import type { ReactNode } from "react";
import { Checkbox, Label } from "@netpro/design-system";
import clsx from "clsx";
import { useId } from "react";

type Props =
  | {
    label: string
    className?: string
    selectable?: boolean
    editable: true
    children: ReactNode
    onToggleSelect?: () => void
    selected?: boolean
  }
  | {
    label: string
    className?: string
    selectable?: boolean
    editable?: false
    children?: never
    onToggleSelect?: () => void
    selected?: boolean
  };

export function MasterClientUserListItem({
  label,
  editable = false,
  selectable = false,
  children,
  className,
  selected = false,
  onToggleSelect,
  ...props
}: Props): ReactNode {
  const fieldId = useId();

  return (
    <li {...props}>
      <Label
        className={clsx(
          "flex cursor-pointer group relative items-center border border-transparent gap-1 justify-between group hover:bg-blue-50/20 transition-all duration-150 px-1 py-2 rounded-sm",
          className,
        )}
        {...(selectable ? { htmlFor: fieldId } : {})}
        title={label}
      >
        {selectable && (
          <Checkbox
            id={fieldId}
            onCheckedChange={onToggleSelect}
            checked={selected}
          />
        )}
        {/* Setting max-w-80 here is a fix for this bug: */}
        {/* https://github.com/radix-ui/primitives/issues/2722 */}
        <span className="pl-1 overflow-hidden max-w-80 min-w-full truncate">
          {label}
        </span>
        <div className="absolute right-2 top-0 bottom-0 flex items-center">
          {editable && children}
        </div>
      </Label>
    </li>
  );
}
