import { z } from "zod";

function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

function preprocessDate(value: unknown): string | undefined {
  if (typeof value === "string" && !Number.isNaN(Date.parse(value))) {
    return formatDate(new Date(value)); // Valid date, format it
  }

  if (value instanceof Date && !Number.isNaN(value.getTime())) {
    return formatDate(new Date(value)); // Valid date, format it
  }

  return undefined;
}

export const searchSchema = z.object({
  columns: z.string().array().optional(),
  filingYear: z.string().optional(),
  isExported: z.string().optional(),
  submittedAfter: z.preprocess(preprocessDate, z.string().optional()),
  submittedBefore: z.preprocess(preprocessDate, z.string().optional()),
  country: z.string().optional(),
  search: z.string().optional(),
  location: z.string().optional(),
});

export type SearchSchemaType = z.infer<typeof searchSchema>
