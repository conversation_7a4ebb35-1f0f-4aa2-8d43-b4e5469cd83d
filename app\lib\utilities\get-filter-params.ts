import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import type { PaginationParams } from "~/lib/hooks/usePaginationParams";
import type { ActiveStatus } from "~/lib/types/filters";
import { getPaginationParams } from "~/lib/utilities/get-pagination-params";

type Props = {
  request: LoaderFunctionArgs["request"] | ActionFunctionArgs["request"]
}

export type FilterParams = {
  searchTerm?: string | null
  isActive?: boolean
  status?: `${ActiveStatus}` // Translates to "active" | "inactive" | "all"
}

type FilterParamsReturnValue<StatusType = ActiveStatus> =
  PaginationParams
  & Pick<FilterParams, "searchTerm">
  & {
    status?: StatusType
  }

export async function getFilterParams<StatusType = ActiveStatus>({ request }: Props): Promise<FilterParamsReturnValue<StatusType>> {
  const paginationParams = await getPaginationParams({ request });
  const url = new URL(request.url);
  const searchTerm = url.searchParams.get("search");
  const status = (url.searchParams.get("status") ?? "all") as StatusType;

  return {
    searchTerm,
    status,
    ...paginationParams,
  };
}
