import { addMonths, isBefore } from "date-fns";
import { z } from "zod";
import { nonEmptyString, nonNullDate, stringBoolean } from "~/lib/utilities/zod-validators";

const fieldName = "This field";

export const resetToSavedSchema = z
  .object({
    comments: nonEmptyString(fieldName),
    correctFinancialPeriod: stringBoolean(),
    correctStartDate: nonNullDate(fieldName).optional(),
    correctEndDate: nonNullDate(fieldName).optional(),
  })
  .superRefine((data, ctx) => {
    const { correctFinancialPeriod, correctStartDate, correctEndDate } = data;

    // Validate: If `correctFinancialPeriod` is "true", both dates must be defined
    if (correctFinancialPeriod === "true") {
      if (!correctStartDate) {
        ctx.addIssue({
          path: ["correctStartDate"],
          code: "custom",
          message: "Correct start date is required when the financial period is marked as true",
        });
      }

      if (!correctEndDate) {
        ctx.addIssue({
          path: ["correctEndDate"],
          code: "custom",
          message: "Correct end date is required when the financial period is marked as true",
        });
      }
    }

    // Validate: Start date must be before end date
    if (correctStartDate && correctEndDate) {
      if (!isBefore(correctStartDate, correctEndDate)) {
        ctx.addIssue({
          path: ["correctStartDate"],
          code: "custom",
          message: "The Financial period start date must be before the Financial period end date",
        });
      }

      // Validate: End date must be within 12 months after start date
      const maxEndDate = addMonths(correctStartDate, 12);
      if (!isBefore(correctEndDate, maxEndDate)) {
        ctx.addIssue({
          path: ["correctEndDate"],
          code: "custom",
          message: "The Financial period end date must be within 12 months after the Financial period start date",
        });
      }
    }
  });

export type ResetToSavedSchemaType = z.infer<typeof resetToSavedSchema>;
