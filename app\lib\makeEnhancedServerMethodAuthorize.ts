import { redirect } from "@remix-run/node";
import type { permissionName } from "~/services/api-generated";
import { getUserPermissions } from "./cookiePermissions.server";

type Args = {
  request: Request
  requiredPermissions: permissionName[]
}

/**
 * Authorization helper to ensure that a user has the required permissions before accessing a
 * specific server-side method or resource. If the user lacks permissions, they are redirected
 * to either the login page or a forbidden page (`/403`). This function has specifically been
 * made to be used inside makeEnhancedServerMethod
 */
export async function makeEnhancedServerMethodAuthorize({ request, requiredPermissions }: Args): Promise<void> {
  const [permissions] = await getUserPermissions({ request });

  // If no permissions are found, redirect to login
  if (!permissions) {
    throw redirect("/login");
  }

  const hasPermission = requiredPermissions.some(permission =>
    permissions.permissions.includes(permission),
  );

  if (!hasPermission) {
    throw redirect("/unauthorized");
  }
}
