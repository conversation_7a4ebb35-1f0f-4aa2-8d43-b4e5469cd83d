import { BalanceSheet } from "~/features/basic-financial-report/components/summary/BalanceSheet";
import { FrontPage } from "~/features/basic-financial-report/components/summary/FrontPage";
import { ProfitLoss } from "~/features/basic-financial-report/components/summary/ProfitLoss";
import { SummaryPage } from "~/features/basic-financial-report/components/summary/SummaryPage";
import { getUnflattenedDataSet } from "~/features/submissions/utilities/submission-data-set";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import type { SubmissionStatus } from "~/services/api-generated";
import { clientGetSubmission } from "~/services/api-generated";

export type BasicFinancialReportSummaryLoader = Promise<{
  submissionData: Record<string, any>
  entityDetails: {
    legalEntityName: string
    status: SubmissionStatus | undefined
    companyIdentityCode: string | undefined | null
    masterClientCode: string | undefined | null
    submittedAt: string | undefined | null
  }
}>
export const loader = makeEnhancedLoader(async ({ request, params }) => {
  await middleware(["auth"], request);
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found");
  }

  const submissionData = getUnflattenedDataSet(submission);
  const entityDetails = {
    legalEntityName: submission.legalEntityName || "No entity name",
    status: submission.status,
    companyIdentityCode: submission.legalEntityCode,
    masterClientCode: submission.masterClientCode,
    submittedAt: submission.submittedAt,
  }

  return {
    submissionData,
    entityDetails,
  };
})

export default function BasicFinancialReportSummary(): JSX.Element {
  return (
    <>
      <SummaryPage>
        <FrontPage />
      </SummaryPage>
      <SummaryPage>
        <BalanceSheet />
      </SummaryPage>
      <SummaryPage>
        <ProfitLoss />
      </SummaryPage>
    </>
  )
}
