import { managementGetAppVersion } from "~/services/api-generated/services.gen";
import packageJson from "../../../package.json";
import { authHeaders } from "../auth/utils/auth-headers";

/**
 * Utility function to get the application version from environment variables
 * @returns The application version or "dev" if not set
 */
export function getAppVersion(): string {
  return packageJson.version;
}

/**
 * Utility function to get the API version from the status controller
 * @param request The request object
 * @returns A promise that resolves to the API version or "unknown" if not available
 */
export async function getApiVersion(request: Request): Promise<string> {
  try {
    const response = await managementGetAppVersion({ headers: await authHeaders(request) });

    return response.data?.version || "unknown";
  } catch (error) {
    console.error("Failed to fetch API version:", error);

    return "unknown";
  }
}
