import { Checkbox } from "@netpro/design-system";
import { createColumnHelper } from "@tanstack/react-table";
import { ReadableSubmissionStatusNames } from "~/features/submissions/utilities/submission-status";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import type { ListSubmissionBahamasDTO } from "~/services/api-generated";

type Type = "submissions" | "payments" | "ird-export"

/**
 * This is a helper hook for the table column definitions.
 */
export function useEsTbahColumns(type: Type) {
  const formatColDate = useFormatColDate();
  const columnHelper = createColumnHelper<ListSubmissionBahamasDTO>();
  let columns = [];
  let sortableColumns: string[] = [];
  const sortingEnabled = (column: string): boolean => sortableColumns.map(col => col.toLowerCase()).includes(column.toLowerCase());

  switch (type) {
    case "submissions":
      /**
       * Sortable columns as per API definition:
       * Available values : LegalEntityName, LegalEntityCode, LegalEntityVPCode, MasterClientCode, Status, FinancialYear, CreatedAt, ExportedAt, PaymentMethod, PaymentReceivedAt, PaymentReference
       */
      sortableColumns = [
        "LegalEntityName",
        "LegalEntityCode",
        "LegalEntityVPCode",
        "MasterClientCode",
        "Status",
        "FinancialYear",
        "CreatedAt",
        "ExportedAt",
        "PaymentMethod",
        "PaymentReceivedAt",
        "PaymentReference",
      ]

      columns = [
        columnHelper.accessor("createdByEmail", {
          header: "Email",
          id: "createdByEmail",
          enableSorting: sortingEnabled("createdByEmail"),
        }),
        columnHelper.accessor("legalEntityName", {
          header: "Entity Name",
          id: "legalEntityName",
          enableSorting: sortingEnabled("legalEntityName"),
        }),
        columnHelper.accessor("legalEntityCode", {
          header: "Regulatory Code",
          id: "legalEntityCode",
          enableSorting: sortingEnabled("legalEntityCode"),
        }),
        columnHelper.accessor("masterClientCode", {
          header: "Master Client Code",
          id: "masterClientCode",
          enableSorting: sortingEnabled("masterClientCode"),
        }),
        columnHelper.accessor("status", {
          header: "Status",
          id: "status",
          enableSorting: sortingEnabled("status"),
          cell: data => data.row.original.status ? ReadableSubmissionStatusNames[data.row.original.status] : data.row.original.status,
        }),
        columnHelper.accessor("createdAt", {
          header: "Date Created",
          id: "createdAt",
          enableSorting: sortingEnabled("createdAt"),
          cell: formatColDate("createdAt"),
        }),
        columnHelper.accessor("submittedAt", {
          header: "Latest Submitted",
          id: "submittedAt",
          enableSorting: sortingEnabled("submittedAt"),
          cell: (data) => {
            const submittedAt = data.row.original.submittedAt;
            const initialSubmittedAt = data.row.original.initialSubmittedAt;

            if (submittedAt === initialSubmittedAt) {
              return "";
            }

            return submittedAt ? formatColDate("submittedAt")(data as any) : "";
          },
        }),
        columnHelper.accessor("initialSubmittedAt", {
          header: "Initial Submitted",
          id: "initialSubmittedAt",
          enableSorting: sortingEnabled("initialSubmittedAt"),
          cell: formatColDate("initialSubmittedAt"),
        }),
        columnHelper.accessor("reopenedAt", {
          header: "Re-Opened Date",
          id: "reopenedAt",
          enableSorting: sortingEnabled("reopenedAt"),
          cell: (data) => {
            const reopenedAt = data.row.original.reopenedAt;

            // Only show if there are multiple revisions (reopenedAt will be null if only 1 revision)
            if (!reopenedAt) {
              return ""; // No reopening happened - only 1 revision
            }

            return formatColDate("reopenedAt")(data as any);
          },
        }),
        columnHelper.accessor("incorporationDate", {
          header: "Incorporation Date",
          id: "incorporationDate",
          enableSorting: sortingEnabled("incorporationDate"),
          cell: formatColDate("incorporationDate"),
        }),
        columnHelper.accessor("paymentReceivedAt", {
          header: "Paid Date",
          id: "paymentReceivedAt",
          enableSorting: sortingEnabled("paymentReceivedAt"),
          cell: formatColDate("paymentReceivedAt"),
        }),
        columnHelper.accessor("paymentReference", {
          header: "Payment Ref",
          id: "paymentReference",
          enableSorting: sortingEnabled("paymentReference"),
        }),
        columnHelper.accessor("financialPeriodEndsAt", {
          header: "Financial Period End Date",
          id: "financialPeriodEndsAt",
          enableSorting: sortingEnabled("financialPeriodEndsAt"),
          cell: formatColDate("financialPeriodEndsAt"),
        }),
        columnHelper.accessor("legalEntityReferralOffice", {
          header: "Referral Office",
          id: "legalEntityReferralOffice",
          enableSorting: sortingEnabled("legalEntityReferralOffice"),
        }),
        columnHelper.accessor("hasActivityNone", {
          header: "None",
          id: "hasActivityNone",
          enableSorting: sortingEnabled("hasActivityNone"),
          cell: data => data.row.original.hasActivityNone ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityBankingBusiness", {
          header: "BB",
          id: "hasActivityBankingBusiness",
          enableSorting: sortingEnabled("hasActivityBankingBusiness"),
          cell: data => data.row.original.hasActivityBankingBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityInsuranceBusiness", {
          header: "IB",
          id: "hasActivityInsuranceBusiness",
          enableSorting: sortingEnabled("hasActivityInsuranceBusiness"),
          cell: data => data.row.original.hasActivityInsuranceBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityFundManagementBusiness", {
          header: "FMB",
          id: "hasActivityFundManagementBusiness",
          enableSorting: sortingEnabled("hasActivityFundManagementBusiness"),
          cell: data => data.row.original.hasActivityFundManagementBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityFinanceLeasingBusiness", {
          header: "FLB",
          id: "hasActivityFinanceLeasingBusiness",
          enableSorting: sortingEnabled("hasActivityFinanceLeasingBusiness"),
          cell: data => data.row.original.hasActivityFinanceLeasingBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityHeadquartersBusiness", {
          header: "HQ",
          id: "hasActivityHeadquartersBusiness",
          enableSorting: sortingEnabled("hasActivityHeadquartersBusiness"),
          cell: data => data.row.original.hasActivityHeadquartersBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityShippingBusiness", {
          header: "SB",
          id: "hasActivityShippingBusiness",
          enableSorting: sortingEnabled("hasActivityShippingBusiness"),
          cell: data => data.row.original.hasActivityShippingBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityIntellectualPropertyBusiness", {
          header: "IP",
          id: "hasActivityIntellectualPropertyBusiness",
          enableSorting: sortingEnabled("hasActivityIntellectualPropertyBusiness"),
          cell: data => data.row.original.hasActivityIntellectualPropertyBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityHoldingBusiness", {
          header: "SC",
          id: "hasActivityHoldingBusiness",
          enableSorting: sortingEnabled("hasActivityHoldingBusiness"),
          cell: data => data.row.original.hasActivityHoldingBusiness ? "Yes" : "No",
        }),
      ];
      break;
    case "payments":
      /**
       * Sortable columns as per API definition:
       * Available values : LegalEntityName, LegalEntityCode, LegalEntityVPCode, MasterClientCode, Status, FinancialYear, CreatedAt, ExportedAt, PaymentMethod, PaymentReceivedAt, PaymentReference
       */
      sortableColumns = [
        "LegalEntityName",
        "LegalEntityCode",
        "LegalEntityVPCode",
        "MasterClientCode",
        "Status",
        "FinancialYear",
        "CreatedAt",
        "SubmittedAt",
        "ExportedAt",
        "PaymentMethod",
        "PaymentReceivedAt",
        "PaymentReference",
      ]

      columns = [
        columnHelper.display({
          id: "select",
          header: ({ table }) => (
            <Checkbox
              checked={
                table.getIsAllPageRowsSelected()
                || (table.getIsSomePageRowsSelected() && "indeterminate")
              }
              onCheckedChange={(value) => {
                if (value) {
                  table.toggleAllPageRowsSelected(true);
                } else {
                  table.resetRowSelection(false);
                }
              }}
              className="-translate-x-3 mx-1"
              aria-label="Select all"
            />
          ),
          cell: ({ row }) => (
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={value => row.toggleSelected(Boolean(value))}
              aria-label="Select row"
              className="-translate-x-3 mx-1"
            />
          ),
          enableSorting: false,
          enableHiding: false,
        }),
        columnHelper.accessor("legalEntityName", {
          header: "Entity Name",
          id: "legalEntityName",
          enableSorting: sortingEnabled("legalEntityName"),
        }),
        columnHelper.accessor("legalEntityCode", {
          header: "Regulatory Code",
          id: "legalEntityCode",
          enableSorting: sortingEnabled("legalEntityCode"),
        }),
        columnHelper.accessor("masterClientCode", {
          header: "Master Client Code",
          id: "masterClientCode",
          enableSorting: sortingEnabled("masterClientCode"),
        }),
        columnHelper.accessor("status", {
          header: "Status",
          id: "status",
          enableSorting: sortingEnabled("status"),
        }),
        columnHelper.accessor("financialPeriodEndsAt", {
          header: "Financial Period End Date",
          id: "financialPeriodEndsAt",
          enableSorting: sortingEnabled("financialPeriodEndsAt"),
          cell: formatColDate("financialPeriodEndsAt"),
        }),
        columnHelper.accessor("createdAt", {
          header: "Date Created",
          id: "createdAt",
          enableSorting: sortingEnabled("createdAt"),
          cell: formatColDate("createdAt"),
        }),
        columnHelper.accessor("submittedAt", {
          header: "Submitted Date",
          id: "submittedAt",
          enableSorting: sortingEnabled("submittedAt"),
          cell: formatColDate("submittedAt"),
        }),
      ];
      break;
    case "ird-export":
      /**
       * Sortable columns as per API definition:
       * Available values : LegalEntityName, LegalEntityCode, LegalEntityVPCode, MasterClientCode, Status, FinancialYear, CreatedAt, ExportedAt, PaymentMethod, PaymentReceivedAt, PaymentReference
       */
      sortableColumns = [
        "LegalEntityName",
        "LegalEntityCode",
        "LegalEntityVPCode",
        "MasterClientCode",
        "Status",
        "FinancialYear",
        "CreatedAt",
        "SubmittedAt",
        "ExportedAt",
        "PaymentMethod",
        "PaymentReceivedAt",
        "PaymentReference",
      ]
      columns = [
        columnHelper.display({
          id: "select",
          header: ({ table }) => (
            <Checkbox
              checked={
                table.getIsAllPageRowsSelected()
                || (table.getIsSomePageRowsSelected() && "indeterminate")
              }
              onCheckedChange={(value) => {
                if (value) {
                  table.toggleAllPageRowsSelected(true);
                } else {
                  table.resetRowSelection(false);
                }
              }}
              className="-translate-x-3 mx-1"
              aria-label="Select all"
            />
          ),
          cell: ({ row }) => (
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={value => row.toggleSelected(Boolean(value))}
              aria-label="Select row"
              className="-translate-x-3 mx-1"
            />
          ),
          enableSorting: false,
          enableHiding: false,
        }),
        columnHelper.display({
          id: "entityName",
          header: "Entity Name",
          cell: props => props.row.original.legalEntityName,
        }),
        columnHelper.display({
          id: "entityNumber",
          header: "Regulatory Code",
          cell: props => props.row.original.legalEntityCode,
        }),
        columnHelper.display({
          id: "status",
          header: "Status",
          cell: props => props.row.original.status,
        }),
        columnHelper.accessor("submittedAt", {
          header: "Submitted Date",
          id: "submittedAt",
          enableSorting: sortingEnabled("submittedAt"),
          cell: formatColDate("submittedAt"),
        }),
        columnHelper.accessor("exportedAt", {
          header: "Exported At",
          id: "exportedAt",
          enableSorting: sortingEnabled("exportedAt"),
          cell: formatColDate("exportedAt"),
        }),
        columnHelper.accessor("initialSubmittedAt", {
          header: "Resubmitted Date",
          id: "initialSubmittedAt",
          enableSorting: sortingEnabled("resubmittedAt"),
          cell: formatColDate("initialSubmittedAt"),
        }),
        columnHelper.accessor("financialPeriodStartsAt", {
          header: "Financial Period Start Date",
          id: "financialPeriodStartsAt",
          enableSorting: sortingEnabled("financialPeriodStartsAt"),
          cell: formatColDate("financialPeriodStartsAt"),
        }),
        columnHelper.accessor("financialPeriodEndsAt", {
          header: "Financial Period End Date",
          id: "financialPeriodEndsAt",
          enableSorting: sortingEnabled("financialPeriodEndsAt"),
          cell: formatColDate("financialPeriodEndsAt"),
        }),
        columnHelper.display({
          id: "paymentMethod",
          header: "Payment Method",
          cell: props => props.row.original.paymentMethod,
        }),
        columnHelper.display({
          id: "datePaid",
          header: "Paid Date",
          cell: formatColDate("paymentReceivedAt"),
        }),
        columnHelper.display({
          id: "paymentRef",
          header: "Payment Ref",
          cell: props => props.row.original.paymentReference,
        }),
      ]
  }

  return { columns }
}
