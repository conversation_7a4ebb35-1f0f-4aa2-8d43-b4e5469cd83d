import { addMonths, isBefore } from "date-fns";
import { z } from "zod";
import { nonNullDate } from "~/lib/utilities/zod-validators";

const fieldName = "This field";

export const changeFinancialPeriodSchema = z
  .object({
    startFinancialDate: nonNullDate({ fieldName }),
    endFinancialDate: nonNullDate({ fieldName }),
  })
  .refine(
    (data) => {
      if (!data.startFinancialDate || !data.endFinancialDate) {
        return true; // Skip if dates are not set
      }

      return isBefore(data.startFinancialDate, data.endFinancialDate); // start date must be before end date
    },
    {
      message: "The Financial period start date must be before the Financial period end date",
      path: ["startFinancialDate"], // Indicate the error is related to the start date
    },
  )
  .refine(
    (data) => {
      if (!data.startFinancialDate || !data.endFinancialDate) {
        return true; // Skip if dates are not set
      }

      const maxEndDate = addMonths(data.startFinancialDate, 12);

      return isBefore(data.endFinancialDate, maxEndDate); // end date must be within 12 months
    },
    {
      message: "The Financial period end date must be within 12 months after the Financial period start date",
      path: ["endFinancialDate"], // Indicate the error is related to the end date
    },
  );

export type ChangeFinancialPeriodSchemaType = z.infer<typeof changeFinancialPeriodSchema>;
