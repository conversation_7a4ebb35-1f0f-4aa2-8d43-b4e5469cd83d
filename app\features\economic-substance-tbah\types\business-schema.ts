import type { z } from "zod";
import { cigaSchema } from "./ciga-schema";
import { directionManagementSchema } from "./direction-management-schema";
import { employeesSchema } from "./employee-schema";
import { expenditureSchema } from "./expenditure-schema";
import { incomeSchema } from "./income-schema";
import { premisesSchema } from "./premises-schema";

export const businessSchema = incomeSchema
  .and(expenditureSchema)
  .and(employeesSchema)
  .and(premisesSchema)
  .and(directionManagementSchema)
  .and(cigaSchema)

export type BusinessSchemaType = z.infer<typeof businessSchema>
