import type { JSX } from "react";
import { Button, Label } from "@netpro/design-system";

type Props = {
  label: string
  buttonText: string
  onClick: () => void
};

export function ActionButton({ label, buttonText, onClick }: Props): JSX.Element {
  return (
    <div className="flex justify-between items-center">
      <Label className="font-semibold text-xs">{label}</Label>
      <Button
        variant="outline"
        size="sm"
        className="text-blue-800 text-xs font-semibold"
        onClick={onClick}
      >
        {buttonText}
      </Button>
    </div>
  )
}
