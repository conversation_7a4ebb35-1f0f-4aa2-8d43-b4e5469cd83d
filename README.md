<p align="center">
  <a href="https://www.netprogroup.com/">
    <img alt="NetPro Group" src="https://www.netprogroup.com/assets/files/logo-blauw.svg" width="200" />
  </a>
</p>

# Trident Trust - Management Portal PCP

This is the repository for the management portal of the Private Client Portal (PCP) project for Trident Trust.

## Documentation & references

- 📖 [Remix docs](https://remix.run/docs)

## Instructions to run project

1. **For Windows users:**

    Connect to the NetPro internal NPM registry by running the following command from the project root:

    ```sh
    # Only needed if you run on Windows
    npx vsts-npm-auth -config .npmrc
    ```

    This will install the vsts-npm-auth package and run it to authenticate with the NetPro NPM registry.
1. Run the following command to install all necessary dependencies:

    ```sh
    npm install
    ```

1. Run the following command to start the project in development mode:

    ```sh
    npm run dev
    ```

## Styling

This project comes with [Tailwind CSS](https://tailwindcss.com/) already configured for a simple default starting experience.

## API Integration

Backend services are automatically generated using `hey-api`. Unfortunately, due to the constraints of the Azure environment, services cannot be automatically generated in production and must be manually generated by the developer. If you find that some backend services are missing in your project, follow these steps:

1. Ensure your local backend is up-to-date and running. Confirm that the required service(s) are available in the [Swagger documentation](https://localhost:7108/swagger/index.html), as this will be used to generate the services for the frontend.
2. Run the command: `npm run generate-api-service`.
3. **Important:** Do not reformat or manually modify the code in `app/services/api-generated/**` as it will be overwritten by future runs.

### Handling Merge Conflicts

Merge conflicts may occur due to this process. If this happens, simply delete the existing generated services and regenerate them, ensuring you are using the latest version of the local API.

## **Forms**
The form components in this application are built with modularity and reusability in mind. These components are organized under `app/components/Form` and leverage a streamlined approach using the `app/lib/makeFormField` utility. This approach reduces boilerplate and provides a consistent interface for handling form logic and UI rendering.

### **Form Element Components**
All form element components (e.g., input, select, etc.) are composed using the `makeFormField` helper function. This utility simplifies integration with `react-hook-form` by taking care of repetitive tasks such as field validation, message rendering, and label association. To get started, refer to the existing `Form...` (eg. `app/components/FormInput`) components for examples and usage patterns.

### **Search Forms**
For pages that include **overview tables** (typically implemented using `app/components/EnhancedTable`), search and filter functionality is often required. This is particularly useful for providing users with a way to refine data in tables dynamically.

#### **Search Parameters**
Search and filter values for these tables are stored in the **URL search parameters**, ensuring persistence and shareability of search results. The `query-string` package is used to handle the serialization and parsing of these parameters.

#### **Filtering Columns**
To manage column-specific filtering, use the `FilterColumns` component located at `app/components/ui/filters/FilterColumns`. This component integrates seamlessly with the search functionality and allows for flexible column-based filtering. For a practical implementation, refer to the `app/routes/_main._card.bo-directors.overview` route.

## EnhancedTable
### Column Ordering
Column ordering is automatically handled by the EnhancedTable component, the component is only responsible for settings the searchParams, the the developer is responsible for mapping the search params to the right API params

If the API provides an enum/union for the sortable columns consider using `app/lib/makeMakeColumn` this utility function can help map the sortable columns into a ColumnDef for the EnhancedTable in a typesafe way.
