import type { FC, PropsWithChildren } from "react";

type Props = { label: string } & PropsWithChildren

export const ActionSheetActionRow: FC<Props> = ({ label, children }) => {
  return (
    <div className="grid grid-cols-2 items-center mb-2">
      <span className="font-semibold text-sm">{label}</span>
      <div className="justify-self-end flex items-center">
        {children}
      </div>
    </div>
  )
}
