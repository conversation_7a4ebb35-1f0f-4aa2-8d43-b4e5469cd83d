import type { ReactNode } from "react";
import { SelectItem } from "@netpro/design-system";
import { useLoaderData, useNavigation } from "@remix-run/react";
import { createColumnHelper } from "@tanstack/react-table";
import { z } from "zod";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { PageErrorBoundary } from "~/components/errors/PageErrorBoundary";
import { FilterFormSearch } from "~/components/FilterFormSearch";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormMultiSelect } from "~/components/FormMultiSelect";
import { FormSelect } from "~/components/FormSelect";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { type BoDirItemDTO, managementListBoDirs } from "~/services/api-generated";

const columnHelper = createColumnHelper<BoDirItemDTO>()
const columns = [
  columnHelper.accessor("directorName", { header: "Email", id: "email" }),
  columnHelper.accessor("directorName", { header: "Entity Name", id: "entityName" }),
  columnHelper.accessor("directorName", { header: "Registration Code", id: "registrationCode" }),
  columnHelper.accessor("directorName", { header: "Master Client Code", id: "masterClientCode" }),
  columnHelper.accessor("directorName", { header: "Incorporation Number", id: "incorporationNumber" }),
  columnHelper.accessor("directorName", { header: "Incorporation Date", id: "incorporationDate" }),
  columnHelper.accessor("directorName", { header: "Status", id: "status" }),
  columnHelper.accessor("directorName", { header: "Date Created", id: "dateCreated" }),
  columnHelper.accessor("directorName", { header: "Date Submitted", id: "dateSubmitted" }),
  columnHelper.accessor("directorName", { header: "FP Start Date", id: "fpStartDate" }),
  columnHelper.accessor("directorName", { header: "FP End Date", id: "fpEndDate" }),
  columnHelper.accessor("directorName", { header: "Referral Office", id: "referralOffice" }),
  columnHelper.accessor("directorName", { header: "Exempt Company", id: "exemptCompany" }),
]

export const handle = {
  breadcrumb: {
    label: "Financial Return",
    to: "/financial-return/overview",
  },
  title: "Overview",
}

export const loader = makeEnhancedLoader(async ({ request }) => {
  await middleware(["auth"], request);
  const { data: paginatedFinancialReturn, error } = await managementListBoDirs({ headers: await authHeaders(request), query: {
  } })

  if (error) {
    throw new Response("Unable to retrieve financial reports", { status: 412 });
  }

  return { paginatedFinancialReturn };
});

const statuses = ["Saved", "In progress", "Confirmed", "Completed", "Under review", "Help request", "Help in progress", "Help completed", "Re-open", "Information request", "In penalty", "Deleted"]
const serviceTypes = ["Complete the Company's Annual Return in the prescribed format", "Prepare the Company's Annual Return using the Trident Accounting Portal", "Have Trident Trust complete the company's annual return in the prescribed format", "Drop Accounting Records for assistance", "Show only exempt company"]

export default function FinancialReturnOverview(): ReactNode {
  const navigation = useNavigation();
  const { formMethods } = useFilterForm(z.object({}));
  const { paginatedFinancialReturn } = useLoaderData<typeof loader>();

  return (
    <CardContainer>
      <Form formMethods={formMethods}>
        <FilterRow cols={4}>
          <FormColumnsFilter
            label="Visible Columns"
            columns={columns}
          />
          <FormMultiSelect
            name="status"
            label="Status"
            triggerLabel="All"
            options={statuses.map(status => ({ checked: false, key: status, label: status }))}
          />
          <FormMultiSelect
            name="serviceType"
            label="Service Type"
            triggerLabel="All"
            options={serviceTypes.map(status => ({ checked: false, key: status, label: status }))}
          />
          <FormSelect
            name="2"
            label="Allow re-open"
            selectValueProps={{ placeholder: "All" }}
            options={[
              <SelectItem key="Yes" value="Yes">
                Yes
              </SelectItem>,
              <SelectItem key="No" value="No">
                No
              </SelectItem>,
            ]}
          />
        </FilterRow>
        <FilterRow cols={4}>
          <FormSelect
            name="3"
            label="Accounting module"
            selectValueProps={{ placeholder: "All" }}
            options={[
              <SelectItem key="Inactive" value="Inactive">
                Inactive
              </SelectItem>,
              <SelectItem key="Active" value="Active">
                Active
              </SelectItem>,
            ]}
          />
          <FormSelect
            name="1"
            label="Show submitted"
            selectValueProps={{ placeholder: "All" }}
            options={[
              <SelectItem key="Yes" value="Yes">
                Yes
              </SelectItem>,
              <SelectItem key="No" value="No">
                No
              </SelectItem>,
            ]}
          />
          <FormDatePicker name="5" label="Submitted Date" />
          <FormDatePicker name="6" label="Incorporation Date" />
        </FilterRow>
        <FilterRow cols={4}>
          <FormDatePicker name="7" label="Financial Period End Date" />
        </FilterRow>
        <FilterFormSearch placeholder="Search by Incorporation Number, Entity Name, Master Client Code, etc." />
      </Form>

      <EnhancedTableContainer>
        <EnhancedTable
          rowId="id"
          columns={columns}
          returnURL="/financial-return/overview"
          sheetURL={row => `/financial-return/overview/${row.id}`}
          data={paginatedFinancialReturn.data}
          totalItems={paginatedFinancialReturn.totalItemCount}
          loading={<LoadingState isLoading={navigation.state === "loading"} />}
        />
      </EnhancedTableContainer>
    </CardContainer>
  )
}

export const ErrorBoundary = PageErrorBoundary;
