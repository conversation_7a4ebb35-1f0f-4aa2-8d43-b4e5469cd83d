import { z } from "zod";
import { stringNumber } from "~/lib/utilities/zod-validators";

export const expenditureSchema = z.object({
  totalExpenditureRelevantActivity: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0, allowDecimal: true }),
  totalExpenditureBahamas: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0, allowDecimal: true }),
})

export type ExpenditureSchemaType = z.infer<typeof expenditureSchema>

export function getExpenditureDefaultValues(data: ExpenditureSchemaType | undefined): ExpenditureSchemaType {
  return {
    totalExpenditureRelevantActivity: data?.totalExpenditureRelevantActivity ?? "",
    totalExpenditureBahamas: data?.totalExpenditureBahamas ?? "",
  };
}
