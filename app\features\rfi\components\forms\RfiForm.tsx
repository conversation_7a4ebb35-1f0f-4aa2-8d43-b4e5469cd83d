import type { ReactNode } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON>le,
  FileList,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Form as NetProForm,
  Spinner,
} from "@netpro/design-system";
import { useLoaderData, useNavigation, useParams, useSubmit } from "@remix-run/react";
import { Form as RemixForm } from "@remix-run/react/dist/components";
import { formatISO } from "date-fns";
import { Repeat } from "lucide-react";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormTextarea } from "~/components/FormTextarea";
import { FileUploader } from "~/features/economic-substance-tbah/components/FileUploader";
import type { RfiSchemaType } from "~/features/economic-substance-tbah/types/rfi-schema";
import { rfiSchema } from "~/features/economic-substance-tbah/types/rfi-schema";
import { usePreserveQueryNavigate } from "~/lib/hooks/usePreserveQueryNavigate";
import type { ManagementGetSubmissionResponse } from "~/services/api-generated";

type GenericLoaderResponse = { submission: ManagementGetSubmissionResponse }
const MAX_FILES = 3

export function RfiForm({ routeName }: { routeName: string }): ReactNode {
  const navigate = usePreserveQueryNavigate();
  const navigation = useNavigation()
  const submit = useSubmit()
  const { id } = useParams()
  const { submission } = useLoaderData<GenericLoaderResponse>();
  const isSubmitting = navigation.state === "submitting"
  const [isOpen, setIsOpen] = useState(false)
  // form logic
  const form = useForm<RfiSchemaType>({
    resolver: zodResolver(rfiSchema),
    defaultValues: {
      deadLine: undefined,
      comments: "",
      files: [],
    },
  });
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    if (!open) {
      navigate(`/${routeName}/${id}`)
    }
  }

  function onSubmit(data: RfiSchemaType): void {
    const formData = new FormData()
    if (data.deadLine) {
      formData.append("deadLine", formatISO(data.deadLine))
    }

    formData.append("comments", data.comments)
    const filesData = data.files
    if (filesData && filesData.length > 0) {
      filesData.forEach((fileData) => {
        formData.append("files", fileData)
      });
    }

    submit(formData, { encType: "multipart/form-data", method: "post" })
  }

  useEffect(() => {
    setIsOpen(true)
  }, [])

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent>
        <FormProvider {...form}>
          <NetProForm {...form}>
            <RemixForm
              onSubmit={form.handleSubmit(onSubmit)}
              method="post"
              noValidate
            >
              <DialogHeader className="flex flex-row items-center gap-2 pb-5">
                <Repeat className="text-primary" />
                <DialogTitle>
                  Request For Information
                </DialogTitle>
              </DialogHeader>
              <div>
                You are about to request information for the following company:
                <ul className="list-disc px-5 text-sm pt-4">
                  <li>
                    {submission.legalEntityName}
                  </li>
                </ul>
              </div>
              <FormDatePicker
                name="deadLine"
                label="Request to respond deadline*"
                datePickerProps={{
                  disabled: isSubmitting,
                  disabledDates: { before: new Date() },
                } as any}
                formItemProps={{ className: "w-full" }}
              />
              <FormTextarea
                name="comments"
                label="Please specify the information that the client has to provide*"
                textareaProps={{ disabled: isSubmitting }}
              />
              <FormField
                control={form.control}
                name="files"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Optional: Upload supporting document(s)
                    </FormLabel>
                    <FormControl>
                      <>
                        <FileUploader
                          maxFiles={MAX_FILES}
                          files={field.value}
                          allowedTypes={["application/pdf"]}
                          setFiles={field.onChange}
                          disabled={isSubmitting}
                        >
                          <div className="flex flex-col gap-1">
                            <p>
                              Drag and drop files here, or click to select files
                            </p>
                            <p className="text-sm text-gray-400">
                              Max. of 4 file(s), PDF only, File must not be password
                              protected.
                              Files will be automatically uploaded
                            </p>
                          </div>
                        </FileUploader>
                        <FileList
                          files={field.value}
                          setFiles={field.onChange}
                        />
                      </>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter className="pt-4">
                <Button
                  disabled={isSubmitting}
                  variant="outline"
                  type="button"
                  onClick={() => handleOpenChange(false)}
                >
                  Cancel
                </Button>
                <Button
                  disabled={isSubmitting}
                  type="submit"
                >
                  {isSubmitting ? <Spinner className="size-4 mx-0 text-white" /> : "Confirm"}
                </Button>
              </DialogFooter>
            </RemixForm>
          </NetProForm>
        </FormProvider>
      </DialogContent>
    </Dialog>

  )
}
