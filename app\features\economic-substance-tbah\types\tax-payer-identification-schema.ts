import { z } from "zod";
import { nonEmptyString, preprocessArray, stringBoolean, stringNumber } from "~/lib/utilities/zod-validators";
import { fileSchema } from "./file-schema";

export const currency = "USD - United States Dollar"
export const parentEntitySchema = z.object({
  name: nonEmptyString("Parent Entity Name"),
  alternativeName: z.string().optional(),
  jurisdictionOfFormation: nonEmptyString("Parent Entity's Jurisdiction of Formation "),
  incorporationNumber: nonEmptyString("Parent Entity's Incorporation Number"),
  taxpayerIdentificationNumber: nonEmptyString("Parent Entity TaxPayer Identification Number"),
})

export type ParentEntitySchemaType = z.infer<typeof parentEntitySchema>

export const taxPayerIdentificationSchema = z
  .object({
    isBahamianResident: stringBoolean(),
    files_BusinessLicense: fileSchema.optional(),
    isInvestmentFund: stringBoolean().optional(),
    entityGrossTotalAnnualIncomeCurrency: nonEmptyString("Field", true),
    entityGrossTotalAnnualIncomeAmount: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0, allowDecimal: true, optional: true }),
    isPartOfMneGroup: stringBoolean().optional(),
    mneGroupName: nonEmptyString("Field", true),
    intendsToClaimTaxResidencyOutsideBahamas: stringBoolean().optional(),
    taxResidencyJurisdiction: nonEmptyString("Field", true),
    taxPayerIdentificationNumber: nonEmptyString("Field", true),
    files_TaxPayerEvidence: fileSchema.optional(),
    hasUltimateParentEntity: stringBoolean().optional(),
    ultimateParentEntities: preprocessArray(z.array(parentEntitySchema)).optional(),
    hasImmediateParentEntity: stringBoolean().optional(),
    immediateParentEntities: preprocessArray(z.array(parentEntitySchema)).optional(),
  })
  .superRefine((data, ctx) => {
    // 1. Validation: If isBahamianResident is "true", files_BusinessLicense is required
    if (data.isBahamianResident === "true" && !data.files_BusinessLicense) {
      ctx.addIssue({
        code: "custom",
        path: ["files_BusinessLicense"],
        message: "Business License is required for Bahamian residents.",
      });
    }

    // 2. Validation: If isBahamianResident is "false", isInvestmentFund is required
    if (data.isBahamianResident === "false" && data.isInvestmentFund === undefined) {
      ctx.addIssue({
        code: "custom",
        path: ["isInvestmentFund"],
        message: "Required",
      });
    }

    if (data.isInvestmentFund !== "false") {
      return;
    }

    // 3. If isInvestmentFund is "false", validate additional rules
    if (data.isInvestmentFund === "false") {
      // 3.1. entityGrossTotalAnnualIncomeCurrency and entityGrossTotalAnnualIncomeAmount are required
      if (!data.entityGrossTotalAnnualIncomeCurrency || !data.entityGrossTotalAnnualIncomeAmount) {
        ctx.addIssue({
          code: "custom",
          path: ["entityGrossTotalAnnualIncomeCurrency"],
          message: "Required",
        });
        ctx.addIssue({
          code: "custom",
          path: ["entityGrossTotalAnnualIncomeAmount"],
          message: "Required",
        });
      }

      // 3.2. isPartOfMneGroup is required
      if (data.isPartOfMneGroup === undefined) {
        ctx.addIssue({
          code: "custom",
          path: ["isPartOfMneGroup"],
          message: "Required",
        });
      } else if (data.isPartOfMneGroup === "true" && !data.mneGroupName) {
        ctx.addIssue({
          code: "custom",
          path: ["mneGroupName"],
          message: "Required",
        });
      }

      // 3.3. intendsToClaimTaxResidencyOutsideBahamas is required
      if (data.intendsToClaimTaxResidencyOutsideBahamas === undefined) {
        ctx.addIssue({
          code: "custom",
          path: ["intendsToClaimTaxResidencyOutsideBahamas"],
          message: "Required",
        });
      } else if (data.intendsToClaimTaxResidencyOutsideBahamas === "true") {
        if (!data.taxResidencyJurisdiction) {
          ctx.addIssue({
            code: "custom",
            path: ["taxResidencyJurisdiction"],
            message: "Required.",
          });
        }

        if (!data.taxPayerIdentificationNumber) {
          ctx.addIssue({
            code: "custom",
            path: ["taxPayerIdentificationNumber"],
            message: "Required.",
          });
        }

        if (!data.files_TaxPayerEvidence) {
          ctx.addIssue({
            code: "custom",
            path: ["files_TaxPayerEvidence"],
            message: "Required.",
          });
        }

        if (!data.hasUltimateParentEntity
          || (data.hasUltimateParentEntity === "true" && (!data.ultimateParentEntities || data.ultimateParentEntities.length === 0))) {
          ctx.addIssue({
            code: "custom",
            path: ["ultimateParentEntities", 0],
            message: "At least one item is required",
          });
        }

        if (!data.hasImmediateParentEntity
          || (data.hasImmediateParentEntity === "true" && (!data.immediateParentEntities || data.immediateParentEntities.length === 0))) {
          ctx.addIssue({
            code: "custom",
            path: ["immediateParentEntities", 0],
            message: "At least one item is required",
          });
        }
      }
    }
  });

export type TaxPayerIdentificationSchemaType = z.infer<typeof taxPayerIdentificationSchema>;
