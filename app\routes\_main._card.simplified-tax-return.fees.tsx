import type { ReactNode } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@netpro/design-system";
import { useLocation, useNavigate, useNavigation } from "@remix-run/react";
import { useLoaderData } from "@remix-run/react/dist/components";
import { createColumnHelper } from "@tanstack/react-table";
import { Plus } from "lucide-react";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { useFeeColumns } from "~/features/fees/hooks/useFeeColumns";
import { useUserHasPermission } from "~/hooks/use-user-has-permission";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { Jurisdictions } from "~/lib/utilities/jurisdictions";
import { requireActiveJurisdiction } from "~/lib/utilities/require-active-jurisdiction";
import { type FeeSettingsDTO, getJurisdictionSettings, type STRLatePaymentFeeDTO } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Fees",
    to: "/simplified-tax-return/fees",
  },
  title: "Fees",
};

export const loader = makeEnhancedLoader(async ({ json, request }) => {
  await middleware(["auth"], request);
  const { jurisdiction: strJurisdiction } = await requireActiveJurisdiction({ request, code: Jurisdictions.NEVIS });
  const { data: feeData } = await getJurisdictionSettings({ headers: await authHeaders(request), path: { jurisdictionId: strJurisdiction.id || "" } })

  return json({
    strLatePaymentFees: feeData,
  })
}, { authorize: ["str.late-payments.view", "str.fee.view"] });

export type FeesLoaderData = typeof loader

export default function Fees(): ReactNode {
  const navigate = useNavigate();
  const location = useLocation();
  const canEditLateFee = useUserHasPermission({ oneOf: ["str.late-payments.set"] });
  const canViewLateFee = useUserHasPermission({ oneOf: ["str.late-payments.view"] });
  const canEditStrFee = useUserHasPermission({ oneOf: ["str.fee.set"] });
  const onRowLateFeeClick = canEditLateFee ? {} : { onRowClick: () => false };
  const onRowStrClick = canEditStrFee ? {} : { onRowClick: () => false };
  const { strLatePaymentFees } = useLoaderData<typeof loader>();

  if (!strLatePaymentFees) {
    throw new Error("The information for str and late str fee payments must be retrieved")
  }

  const strLatePaymentFeesData = strLatePaymentFees?.strLatePaymentFeeSettings?.strLatePaymentFees || []
  const feeSettingsData = strLatePaymentFees.feeSettings
  const hasStrFeeSettingsData = feeSettingsData && feeSettingsData.strSubmissionFee !== null
  const navigation = useNavigation();
  const { columnsLateFee } = useFeeColumns("late-fee-management");
  const columnHelper = createColumnHelper<FeeSettingsDTO>();
  const columnsFeeSettings = [
    columnHelper.display({ id: "strSubmissionFeeInvoiceText", header: "Description", cell: props => props.row.original.strSubmissionFeeInvoiceText }),
    columnHelper.display({ id: "strSubmissionFee", header: "Fee", cell: props => props.row.original.strSubmissionFee }),
  ]

  if (!strLatePaymentFees) {
    return <div>Error: Data not loaded</div>;
  }

  return (
    <Tabs className="w-full" defaultValue={canViewLateFee || canEditLateFee ? "late-fee" : "str-fee"}>
      <TabsList className="grid max-w-80 grid-cols-2 border-b-0">
        <Authorized oneOf={["str.late-payments.view"]}>
          <TabsTrigger value="late-fee">Late Fee</TabsTrigger>
        </Authorized>
        <Authorized oneOf={["str.fee.view"]}>
          <TabsTrigger value="str-fee">STR Fee</TabsTrigger>
        </Authorized>
      </TabsList>
      <Authorized oneOf={["str.late-payments.view"]}>
        <TabsContent value="late-fee">
          <CardContainer>
            <Authorized oneOf={["str.late-payments.set"]}>
              <div>
                <Button type="button" variant="default" onClick={() => navigate("/simplified-tax-return/fees/late/create")} className="mr-2">
                  <Plus size={15} />
                  Set Late Fee
                </Button>
              </div>
            </Authorized>
            <div className="h-full my-16 overflow-hidden">
              <h2 className="font-bold">Late Fee Payment</h2>
              <EnhancedTable
                {...onRowLateFeeClick}
                sheetURL={row => `/simplified-tax-return/fees/late/${row.id}`}
                returnURL="/simplified-tax-return/fees"
                data={strLatePaymentFeesData as STRLatePaymentFeeDTO[]}
                loading={<LoadingState isLoading={navigation.state === "loading"} />}
                rowId="id"
                columns={columnsLateFee}
                totalItems={strLatePaymentFeesData.length}
                defaultOpen={/^\/simplified-tax-return\/fees\/./.test(location.pathname)}
              />
            </div>
          </CardContainer>
        </TabsContent>
      </Authorized>
      <Authorized oneOf={["str.fee.view"]}>
        <TabsContent value="str-fee">
          <Authorized oneOf={["str.fee.set"]}>
            {!hasStrFeeSettingsData && (
              <Button type="button" variant="default" onClick={() => navigate("/simplified-tax-return/fees/create")}>
                <Plus size={15} />
                Set STR Fee
              </Button>
            )}
          </Authorized>

          <div className="h-full my-16 overflow-hidden">
            <div className="font-bold -mb-2">STR Fee</div>
            <span className="text-sm text-gray-500">This is the regular amount that is charged to all submissions for all years, unless they have a deviation on the company level</span>
            {hasStrFeeSettingsData && (
              <EnhancedTable
                {...onRowStrClick}
                rowId="strSubmissionFee"
                sheetURL="/simplified-tax-return/fees/"
                returnURL="/simplified-tax-return/fees"
                data={hasStrFeeSettingsData ? [feeSettingsData as FeeSettingsDTO] : undefined}
                columns={columnsFeeSettings}
                totalItems={1}
                defaultOpen={/^\/simplified-tax-return\/fees\/./.test(location.pathname)}
              />
            )}
          </div>

        </TabsContent>
      </Authorized>
    </Tabs>
  );
}
