import type { UpdateCompanyModulesRequest, UpdateCompanyModulesResponse } from "../types/company";
import { client } from "~/lib/api-client";

export async function sendCompanyModuleUpdateRequest(
  companyId: string,
  userId: string,
  request: UpdateCompanyModulesRequest,
  accessToken: string,
): Promise<UpdateCompanyModulesResponse> {
  return client.put<UpdateCompanyModulesResponse>(
    `/management/companies/${companyId}/modules`,
    accessToken,
    userId,
    request,
  );
}
