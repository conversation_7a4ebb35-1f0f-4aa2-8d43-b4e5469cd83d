import { ActivityEnum } from "../types/ciga-schema";

export const activityOptions = Object.entries({
  NO_CIGA: "0.1 - No CIGA",
  OTHER_PLEASE_SPECIFY: "0.2 - Other",
  RAISING_FUNDS_MANAGING_RISK: "1.1 - Raising funds, managing risk including credit, currency and interest risk.",
  TAKING_HEDGING_POSITIONS: "1.2 - Taking hedging positions.",
  PROVIDING_LOANS_CREDIT_SERVICES: "1.3 - Providing loans, credit or other financial services to customers.",
  MANAGING_REGULATORY_CAPITAL: "1.4 - Managing regulatory capital.",
  PREPARING_REGULATORY_REPORTS: "1.5 - Preparing regulatory reports and returns.",
  PREDICTING_AND_CALCULATING_RISK: "2.1 - Predicting and calculating risk.",
  INSURING_AGAINST_RISK: "2.2 - Insuring or re-insuring against risk.",
  PROVIDING_INSURANCE_SERVICES: "2.3 - Providing insurance business services to clients.",
  DECISIONS_ON_INVESTMENTS: "3.1 - Taking decisions on the holding and selling of investments.",
  CALCULATING_RISK_AND_RESERVES: "3.2 - Calculating risk and reserves.",
  DECISIONS_ON_CURRENCY_FLUCTUATIONS: "3.3 - Taking decisions on currency or interest fluctuations and hedging positions.",
  PREPARING_REGULATORY_REPORTS_GOVERNMENT: "3.4 - Preparing relevant regulatory or other reports for government authorities and investors.",
  AGREEING_FUNDING_TERMS: "4.1 - Agreeing funding terms.",
  IDENTIFYING_AND_ACQUIRING_ASSETS: "4.2 - Identifying and acquiring assets to be leased (in the case of leasing).",
  SETTING_TERMS_AND_DURATION: "4.3 - Setting the terms and duration of any financing or leasing.",
  MONITORING_AND_REVISING_AGREEMENTS: "4.4 - Monitoring and revising any agreements.",
  MANAGING_RISKS: "4.5 - Managing any risks.",
  TAKING_MANAGEMENT_DECISIONS: "5.1 - Taking relevant management decisions.",
  INCURRING_EXPENDITURES_FOR_AFFILIATES: "5.2 - Incurring expenditures on behalf of affiliates.",
  COORDINATING_GROUP_ACTIVITIES: "5.3 - Coordinating group activities.",
  MANAGING_CREW: "6.1 - Managing the crew (including hiring, paying and overseeing crewmembers).",
  MAINTAINING_SHIPS: "6.2 - Hauling and maintaining ships.",
  OVERSEEING_DELIVERIES: "6.3 - Overseeing and tracking deliveries.",
  DETERMINING_GOODS_ORDERING: "6.4 - Determining what goods to order and when to deliver them.",
  ORGANISING_VOYAGES: "6.5 - Organising and overseeing voyages.",
  BUSINESS_WITH_PATENTS: "8.1 - Business concerned with intellectual property assets such as patents, research and development.",
  BUSINESS_WITH_TRADEMARKS: "8.2 - Business concerned with non-trade intangible assets such as brand, trademark and customer data, marketing, branding and distribution.",
  TRANSPORTING_AND_STORING_GOODS: "9.1 - Transporting and storing goods.",
  MANAGING_STOCKS: "9.2 - Managing stocks.",
  TAKING_ORDERS: "9.3 - Taking orders.",
  PROVIDING_CONSULTING_SERVICES: "9.4 - Providing consulting or other administrative services.",
}).map(([key, label]) => ({
  value: ActivityEnum[key as keyof typeof ActivityEnum],
  label,
}));
