import type { ReactNode } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SmartPagination } from "@netpro/design-system";
import { useFetcher, useLocation, useSearchParams } from "@remix-run/react";
import { useState } from "react";
import type { PageRange } from "~/lib/hooks/usePaginationParams";
import { PAGINATION, usePaginationParams } from "~/lib/hooks/usePaginationParams";

type PaginationProps = {
  totalItems: number
  onPageSizeChange?: (pageSize: number) => void
}

/**
 * Pagination Component
 *
 * This component displays paginated information with customizable page size and navigation controls.
 * It shows the current range of items and provides options for users to select the number of rows displayed per page.
 *
 * @param {PaginationProps} props - The properties for the Pagination component.
 * @param {number} props.totalItems - The total number of items to be paginated.
 *
 * @returns {ReactNode} - Returns the pagination control UI.
 *
 * ## Usage
 *
 * ```jsx
 * <Pagination totalItems={100} />
 * ```
 *
 * pageSize and pageNumber are retrieved internally from the URL query parameters using the usePaginationParams hook.
 */
export function Pagination({ totalItems, onPageSizeChange }: PaginationProps): ReactNode {
  const fetcher = useFetcher();
  const { pageSize, pageNumber } = usePaginationParams();
  const [,setSearchParams] = useSearchParams();
  const [pageSizeValue, setPageSizeValue] = useState(pageSize);
  const startIndex = (pageNumber - 1) * pageSizeValue + 1;
  const endIndex = Math.min(startIndex + pageSizeValue - 1, totalItems);
  const location = useLocation();

  return (
    <div className="mt-4 flex justify-between items-center border-t border-t-teal-200 pt-4">
      <div className="text-xs font-bold whitespace-nowrap">
        {`Showing ${startIndex} to ${endIndex} of ${totalItems} entries`}
      </div>
      <div className="flex items-center justify-between w-full">
        <SmartPagination
          totalItems={totalItems}
          pageSize={pageSize}
          currentPage={pageNumber}
        />
        <div className="flex items-center">
          <span className="mr-2 font-bold text-xs whitespace-nowrap">
            Rows per page:
          </span>
          <fetcher.Form>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => {
                setPageSizeValue(Number(value) as PageRange);
                setSearchParams((prev) => {
                  prev.delete("page");

                  return prev;
                })
                onPageSizeChange?.(Number(value));
                fetcher.submit({
                  tablePageSize: Number(value),
                  to: location.pathname,
                }, {
                  method: "post",
                  action: "/user-preferences",
                });
              }}
            >
              <SelectTrigger className="w-20 font-bold">
                {pageSizeValue}
              </SelectTrigger>
              <SelectContent>
                {PAGINATION.PAGE_RANGE.map(value => (
                  <SelectItem key={value} value={value.toString()}>
                    {value}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </fetcher.Form>
        </div>
      </div>
    </div>
  )
}
