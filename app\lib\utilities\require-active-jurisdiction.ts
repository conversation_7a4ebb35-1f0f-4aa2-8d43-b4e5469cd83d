import type { JurisdictionDTO } from "~/services/api-generated";
import { getJurisdictions } from "~/services/api-generated";
import { authHeaders } from "../auth/utils/auth-headers";

type GetActiveJurisdictionParams = {
  code: string
  request: Request
}

export async function getActiveJurisdiction({ request, code }: GetActiveJurisdictionParams): Promise<{
  jurisdiction: JurisdictionDTO | undefined
}> {
  const { data } = await getJurisdictions({ headers: await authHeaders(request), query: {
    active: true,
  } });

  if (!data) {
    throw new Error("Jurisdiction not found");
  }

  const jurisdictions = data?.data || [];

  return {
    jurisdiction: jurisdictions.find(jurisdiction => jurisdiction.code === code),
  }
}

export async function requireActiveJurisdiction({ request, code }: GetActiveJurisdictionParams): Promise<{
  jurisdiction: JurisdictionDTO
}> {
  const { jurisdiction } = await getActiveJurisdiction({ request, code });

  if (!jurisdiction) {
    throw new Error("jurisdiction is not found or disabled");
  }

  return { jurisdiction };
}
