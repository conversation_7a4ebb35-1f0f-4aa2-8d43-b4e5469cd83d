import type { LoaderFunctionArgs } from "@remix-run/node";
import type { JSX } from "react";
import { useLoaderData } from "@remix-run/react";
import type { FormYear } from "~/features/simplified-tax-return/types/form-year";
import { formSummary } from "~/features/simplified-tax-return/utilities/form-summary";
import { SubmissionProvider } from "~/features/submissions/context/SubmissionContextProvider";
import { getUnflattenedDataSet } from "~/features/submissions/utilities/submission-data-set";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import { managementGetSubmission } from "~/services/api-generated";

export async function loader({ request, params }: LoaderFunctionArgs) {
  await middleware(["auth"], request);
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const { data: submission, error } = await managementGetSubmission({ headers: await authHeaders(request), path: { submissionId: id! }, query: {
    includeFormDocument: true,
  } })

  if (error || !submission) {
    throw new Error("Submission not found");
  }

  const submissionData = getUnflattenedDataSet(submission);
  const entityDetails = {
    financialYear: submission.financialYear,
    submittedAt: submission.submittedAt,
    status: submission.status,
    companyCode: submission.legalEntityCode,
    masterClientCode: submission.masterClientCode,
    companyName: submission.legalEntityName,
    isPaid: submission.isPaid,
  }

  return {
    financialYear: submission.financialYear as FormYear,
    submissionData,
    entityDetails,
  };
}

export default function STRSummaryPDF(): JSX.Element {
  const { financialYear, submissionData, entityDetails } = useLoaderData<typeof loader>();
  const currentSummary = formSummary[financialYear];

  return (
    <SubmissionProvider submissionData={submissionData} financialYear={financialYear}>
      {currentSummary({ entityDetails })}
    </SubmissionProvider>
  );
}
