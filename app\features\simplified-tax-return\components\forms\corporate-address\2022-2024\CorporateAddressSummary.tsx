import type { ReactNode } from "react";
import { Table, TableBody, TableCell, TableRow } from "@netpro/design-system";
import type { CorporateAddressType } from "~/features/simplified-tax-return/schemas/corporate-address/corporate-address-schema";
import { Pages } from "~/features/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/features/submissions/context/use-submission";
import { formatYesNoBoolean } from "~/lib/utilities/format";

export function CorporateAddressSummary(): ReactNode {
  const { submissionData } = useSubmission();
  const corporateAddress = submissionData[Pages.CORPORATE_ADDRESS] as CorporateAddressType;

  return (
    <section id="corporate-address-section">
      <Table className="border border-blue-600 pointer-events-none mb-2">
        <TableBody>
          <TableRow className="border border-blue-600">
            <TableCell className="w-2/3 py-1">
              <span>Are the corporation's accounting records kept at the Registered Office of the corporation in Nevis?</span>
            </TableCell>
            <TableCell className="w-1/3 text-center py-1">
              <span className="font-semibold ">{formatYesNoBoolean(corporateAddress?.recordsKeptAtRegisteredOffice)}</span>
            </TableCell>
          </TableRow>
          {corporateAddress?.recordsKeptAtRegisteredOffice === "true" && (
            <TableRow className="border border-blue-600 py-1">
              <TableCell className="w-2/3 py-1">
                <span>Provide the address of the Nevis registered office</span>
              </TableCell>
              <TableCell className="w-1/3 text-center py-1">
                <span className="font-semibold">{corporateAddress?.nevisAddress}</span>
              </TableCell>
            </TableRow>
          )}
          {corporateAddress?.recordsKeptAtRegisteredOffice === "false" && (
            <TableRow className="border border-blue-600 py-1">
              <TableCell className="w-2/3 py-1">
                <span>Provide the place of business/physical address of the corporation/directors where the records are kept</span>
              </TableCell>
              <TableCell className="w-1/3 text-center py-1">
                <span className="font-semibold">{corporateAddress?.recordsPlaceAddress}</span>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </section>
  );
}
