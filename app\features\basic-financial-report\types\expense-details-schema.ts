import { z } from "zod";
import { preprocessArray, stringNumber } from "~/lib/utilities/zod-validators";
import { incomeSchema } from "./income-schema";

export const expenseDetailsSchema = z.object({
  portfolioManagementFeesPeriod: stringNumber({ invalidTypeMessage: "Portfolio management fees for the period are required.", greaterThan: 0 }),
  companyAdministrationFeesPeriod: stringNumber({ invalidTypeMessage: "Company administration fees for the period are required.", greaterThan: 0 }),
  loanInterestPayments: stringNumber({ invalidTypeMessage: "Loan interest payments are required.", greaterThan: 0 }),
  bankCharges: stringNumber({ invalidTypeMessage: "Bank charges are required.", greaterThan: 0 }),
  taxWithheld: stringNumber({ invalidTypeMessage: "Tax withheld is required.", greaterThan: 0 }),
  portfolioManagementFees: stringNumber({ invalidTypeMessage: "Portfolio management fees are required.", greaterThan: 0 }),
  companyAdministrationFees: stringNumber({ invalidTypeMessage: "Company administration fees are required.", greaterThan: 0 }),
  otherCompanyExpenses: preprocessArray(z.array(incomeSchema)),
  beginningInterestPayable: stringNumber({ invalidTypeMessage: "Beginning interest payable is required.", greaterThan: 0 }),
  otherPeriodPaidExpenses: preprocessArray(z.array(incomeSchema)),
  endingInterestPayableLoans: stringNumber({ invalidTypeMessage: "Ending interest payable on loans is required.", greaterThan: 0 }),
  otherPeriodNotPaidExpenses: preprocessArray(z.array(incomeSchema)),
  dividendsPaidShareholders: stringNumber({ invalidTypeMessage: "Dividends paid to shareholders are required.", greaterThan: 0 }),
  dividendsNotPaidShareholders: stringNumber({ invalidTypeMessage: "Dividends not paid to shareholders are required.", greaterThan: 0 }),
  totalPaymentsPurchaseSecuritiesInvestments: stringNumber({ invalidTypeMessage: "Total payments for purchasing securities and investments are required.", greaterThan: 0 }),
})
  .refine(val => val.otherCompanyExpenses.length > 0, { message: "At least one item is required", path: ["otherCompanyExpenses", 0] })
  .refine(val => val.otherPeriodPaidExpenses.length > 0, { message: "At least one item is required", path: ["otherPeriodPaidExpenses", 0] })
  .refine(val => val.otherPeriodNotPaidExpenses.length > 0, { message: "At least one item is required", path: ["otherPeriodNotPaidExpenses", 0] })

export type ExpenseDetailsSchemaType = z.infer<typeof expenseDetailsSchema>;
