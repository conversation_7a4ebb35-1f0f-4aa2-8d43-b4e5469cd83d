import mime from "mime";
import type { DocumentDTO, DocumentType } from "~/services/api-generated";

/**
 * Decodes a base64-encoded file data and returns a Blob, filename, and MIME type.
 * @param {DocumentDTO} fileData  - The file data object.
 * @returns An object containing the Blob, filename, and MIME type.
 */
function decodeFileData(fileData: DocumentDTO) {
  const { documentData, filename, id } = fileData;

  if (!documentData || !filename || !id) {
    throw new Error("file data must have document data, a filename, and an ID");
  }

  // Decode base64 string (ensure documentData is in correct base64 format)
  const byteCharacters = atob(documentData.split(",")[1] || documentData);
  const byteNumbers: number[] = Array.from(byteCharacters, char => char.charCodeAt(0));
  const byteArray = new Uint8Array(byteNumbers);
  // Get MIME type from filename using mime package
  const mimeType = mime.getType(filename) || "application/octet-stream";
  const blob = new Blob([byteArray], { type: mimeType });

  return { blob, filename, mimeType, id };
}

/**
 * Converts decoded file data to a File object.
 * @param {DocumentDTO} fileData - The file data object.
 * @returns A File object created from the decoded file data.
 */
export function fileDataToFile(fileData: DocumentDTO): File {
  const { blob, filename, mimeType, id } = decodeFileData(fileData);

  return new File([blob], filename || `no-name-file-${id}`, { type: mimeType });
}

/**
 * Converts a mapped object of DocumentDTOs to an object of Files.
 * @param {Record<string, DocumentDTO>} mappedDocuments - Object mapping keys to DocumentDTOs.
 * @returns {Record<string, File>} An object with the same keys, but where values are Files.
 */
export function convertMappedDocumentsToFileObject(mappedDocuments: Record<string, DocumentDTO>): Record<string, File> {
  if (typeof mappedDocuments !== "object" || mappedDocuments === null) {
    throw new Error("The input must be a non-null object of type Record<string, DocumentDTO>.");
  }

  const fileObject: Record<string, File> = {};
  for (const [key, document] of Object.entries(mappedDocuments)) {
    fileObject[key] = fileDataToFile(document);
  }

  return fileObject;
}

/**
 * Generates a URL from the decoded file data.
 * @param {DocumentDTO} fileData - The file data object.
 * @returns A URL created from the decoded file data.
 */
export function fileDataToUrl(fileData: DocumentDTO): string {
  const { blob } = decodeFileData(fileData);
  // Generate a URL from the Blob
  const url = URL.createObjectURL(blob);

  return url;
}

/**
 * Determines the document type based on the file's MIME type.
 * @param {File} file - The file whose type is to be determined.
 * @returns {DocumentType} The determined document type:
 * "Image" for image files, "Pdf" for PDF files, "Xls" for Excel files,
 * or "Unknown" for unsupported file types.
 */
export function getDocumentType(file: File): DocumentType {
  const mimeType = file.type;

  if (mimeType.startsWith("image/")) {
    return "Image";
  } else if (mimeType === "application/pdf") {
    return "Pdf";
  } else if (mimeType === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
    return "Xls";
  } else {
    return "Unknown";
  }
}

/**
 * Sanitizes filenames for safe upload according to RFC 7578
 * Removes/replaces dangerous characters that could cause security issues
 */
export function sanitizeFilename(filename: string): string {
  if (!filename) {
    return "unnamed-file";
  }

  // Get file extension
  const lastDotIndex = filename.lastIndexOf(".");
  const name = lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;
  const extension = lastDotIndex > 0 ? filename.substring(lastDotIndex) : "";
  let sanitized = name
    // Remove/replace dangerous characters
    .replace(/['"]/g, "") // Remove quotes entirely
    .replace(/[\\;]/g, "_") // Replace backslash and semicolon with underscore
    .replace(/&/g, "and") // Replace ampersand with 'and'
    .replace(/[<>]/g, "") // Remove HTML brackets
    .replace(/%/g, "") // Remove percent signs
    // Remove control characters (CR, LF, tab, etc.)
    .replace(/\p{C}/gu, "")
    // Replace spaces with underscore
    .replace(/\s+/g, "_")
    // Remove or normalize non-ASCII characters (keep basic accented chars, remove others)
    .replace(/[^\u0020-\u007E\u00C0-\u017F]/g, "")
    // Remove multiple consecutive underscores
    .replace(/_+/g, "_")
    // Remove leading/trailing underscores
    .replace(/^_+|_+$/g, "");

  // Ensure we have something left
  if (!sanitized) {
    sanitized = "sanitized-file";
  }

  // Limit length (keep under 255 characters total)
  if (sanitized.length + extension.length > 200) {
    sanitized = sanitized.substring(0, 200 - extension.length);
  }

  return sanitized + extension;
}
