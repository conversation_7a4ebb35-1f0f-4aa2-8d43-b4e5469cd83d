import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Button, Form as NetProForm, SelectItem } from "@netpro/design-system";
import { Outlet, Form as RemixForm, useLoaderData, useParams, useSubmit } from "@remix-run/react"
import { formatISO, parseISO } from "date-fns";
import { FormProvider, useForm } from "react-hook-form";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormInput } from "~/components/FormInput";
import { FormSelect } from "~/components/FormSelect";
import type { EditLateFeeSchemaType } from "~/features/fees/schemas/edit-late-fee";
import { editLateFeeSchemaClient } from "~/features/fees/schemas/edit-late-fee";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { Jurisdictions } from "~/lib/utilities/jurisdictions";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveJurisdiction } from "~/lib/utilities/require-active-jurisdiction";
import type { STRLatePaymentFeeDTO } from "~/services/api-generated";
import { getJurisdictionSettings, getModules, managementGetAllSubmissionYears, setJurisdictionSettings } from "~/services/api-generated";

export const action = makeEnhancedAction(async ({ request, redirect, setNotification }) => {
  await middleware(["auth"], request);
  const { jurisdiction: strJurisdiction } = await requireActiveJurisdiction({ request, code: Jurisdictions.NEVIS });
  const formData = await request.formData();
  const data = JSON.parse(formData.get("data") as string) as STRLatePaymentFeeDTO
  const body = {
    strLatePaymentFeeSettings: {
      strLatePaymentFees: [
        data,
      ],
    },
  }
  const { error } = await setJurisdictionSettings({
    headers: await authHeaders(request),
    path: { jurisdictionId: strJurisdiction.id || "" },
    body,
  });

  if (error) {
    setNotification({ title: "Failed to create the Late fee", variant: "error" })
  } else {
    setNotification({ title: "Create Late fee successfully", variant: "success" })
  }

  return redirect("/simplified-tax-return/fees")
}, { authorize: ["str.late-payments.set"] });

export const loader = makeEnhancedLoader(async ({ request, json }) => {
  await middleware(["auth"], request);
  const modulesResponse = await getModules({
    headers: await authHeaders(request),
  });
  if (!modulesResponse || !modulesResponse.data?.modules) {
    throw new Response("Modules not found", { status: 404 });
  }

  const simplifiedTaxReturnModule = modulesResponse.data.modules.find(module => module.key === Modules.SIMPLIFIED_TAX_RETURN);
  if (!simplifiedTaxReturnModule) {
    throw new Response("Simplified Tax Return module not found", { status: 404 });
  }

  const moduleId = simplifiedTaxReturnModule.id;
  const submissionYearsResponse = await managementGetAllSubmissionYears({
    headers: await authHeaders(request),
    path: { moduleId: moduleId as string },
  });
  if (!submissionYearsResponse || !submissionYearsResponse.data?.years) {
    throw new Response("Fee years not found", { status: 404 });
  }

  const { jurisdiction: strJurisdiction } = await requireActiveJurisdiction({ request, code: Jurisdictions.NEVIS });
  const feeDataLate = await getJurisdictionSettings({
    headers: await authHeaders(request),
    path: { jurisdictionId: strJurisdiction.id || "" },
  })
  if (!feeDataLate) {
    throw new Response("Fee STR not found", { status: 404 });
  }

  return json({
    feeDataLate: feeDataLate.data?.strLatePaymentFeeSettings,
    years: submissionYearsResponse.data?.years,
  });
}, { authorize: ["str.late-payments.set"] });

export default function StrFeeLateCreation(): JSX.Element {
  const { feeDataLate, years } = useLoaderData<typeof loader>();
  const params = useParams();
  const submit = useSubmit()
  const feeDetails = feeDataLate?.strLatePaymentFees?.find(fee => fee.id === params.id);
  const formMethods = useForm<EditLateFeeSchemaType>({
    resolver: zodResolver(editLateFeeSchemaClient),
    defaultValues: {
      description: feeDetails?.description || "Late filing fee",
      invoiceText: feeDetails?.invoiceText || "Late filing fee",
      startAt: feeDetails?.startAt ? parseISO(feeDetails.startAt) : undefined,
      endAt: feeDetails?.endAt ? parseISO(feeDetails.endAt) : undefined,
      amount: feeDetails?.amount?.toString() || "",
      financialYear: feeDetails?.financialYear?.toString() || "",
    },
  });

  function onSubmit(data: EditLateFeeSchemaType) {
    const { description, invoiceText, startAt, endAt, financialYear, amount } = data;
    const body: STRLatePaymentFeeDTO = {
      description,
      invoiceText,
      startAt: formatISO(startAt),
      endAt: formatISO(endAt),
      financialYear: Number(financialYear),
      amount: Number(amount),
    }
    submit({ _action: "update", data: JSON.stringify(body) }, { method: "post" })
  }

  return (
    <>
      <FormProvider {...formMethods}>
        <NetProForm {...formMethods}>
          <RemixForm onSubmit={formMethods.handleSubmit(onSubmit)} className="h-full grid grid-rows-[auto_1fr_auto]">
            <ActionSheetContent title="Create Late Fee Payment">
              <span className="font-semibold text-xl text-blue-700">Detail</span>
              <FormSelect
                name="financialYear"
                label="Financial Year*"
                selectValueProps={{ placeholder: "Select a financial year" }}
                options={(years || []).map(yearLateSubmission => (
                  <SelectItem key={yearLateSubmission} value={yearLateSubmission.toString()}>
                    {yearLateSubmission}
                  </SelectItem>
                ))}
                selectProps={{ ...formMethods.register("financialYear") }}
              />
              <span className="text-sm font-semibold -mb-5">Fee Start Date*</span>
              <FormDatePicker name="startAt" />
              <span className="text-sm font-semibold -mb-5">Fee End Date*</span>
              <FormDatePicker name="endAt" />
              <span className="text-sm font-semibold -mb-5">Fee*</span>
              <FormInput name="amount" formItemProps={{ className: "w-full" }} inputProps={{ placeholder: "0.00", type: "number" }} />
            </ActionSheetContent>
            <ActionSheetFooter>
              <Button size="sm" type="submit">Create</Button>
            </ActionSheetFooter>
          </RemixForm>
        </NetProForm>
      </FormProvider>
      <Outlet />
    </>
  )
}
