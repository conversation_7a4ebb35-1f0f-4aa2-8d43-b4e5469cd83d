export type Jurisdiction = {
  id: string
  name: string
  code: string
}

export type InvitationDetails = {
  isInvited: boolean
  lastInvitationAt?: string
}

export type MasterClientUser = {
  id: string
  firstName: string
  lastName: string
  displayName: string
  email: string
  isRegistered: boolean
  invitationDetails: InvitationDetails
}

export type MasterClient = {
  id: string
  code: string
  jurisdictions: Jurisdiction[]
  masterClientUsers: MasterClientUser[]
}
