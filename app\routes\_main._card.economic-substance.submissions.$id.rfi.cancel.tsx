import type { ReactNode } from "react";
import { RfiCancelDialog } from "~/features/rfi/components/dialogs/RfiCancelDialog";
import { getRfiCancelAction } from "~/features/rfi/utlities/rfi-cancel-action.server";
import { getRfiLoader } from "~/features/rfi/utlities/rfi-cancel-loader.server";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";

const routeName = "economic-substance/submissions"
export const loader = makeEnhancedLoader(async (args) => {
  try {
    const result = await getRfiLoader(routeName, args);

    return result ?? {};
  } catch (err) {
    if (err instanceof Response && err.status === 404) {
      return {};
    }

    throw err;
  }
}, {
  authorize: ["es.bahamas.rfi-request.start"],
});

export const action = makeEnhancedAction(async (args) => {
  return getRfiCancelAction(routeName, args)
}, {
  authorize: ["es.bahamas.rfi-request.start"],
});

export default function EconomicSubstanceBahamasRfiCancel(): ReactNode {
  return (<RfiCancelDialog routeName={routeName} />)
}
