import type { ReactNode } from "react";
import { Outlet } from "@remix-run/react";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";

export const handle = {
  breadcrumb: {
    label: "Master Clients",
    to: "/master-clients",
  },
  title: "View History Logs",
  /*
   * This is the path to return to when the back button is clicked
   * It's required to define this path to return to the correct page
   */
  returnTo: {
    to: "/master-clients",
    label: "Back to Master Clients",
  },
}

export const loader = makeEnhancedLoader(() => {
  return null
  // todo: currently the permission is not defined
}, { authorize: ["masterclients.view"] })

export default function Layout(): ReactNode {
  return <Outlet />
}
