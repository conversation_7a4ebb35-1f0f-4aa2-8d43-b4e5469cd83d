import { useMemo } from "react";
import { usePaginatedContent } from "~/lib/hooks/usePaginatedContent";
import { AddressOfHeadOfficeSummary } from "../../forms/address-of-head-office/2020-2024/AddressOfHeadOfficeSummary";
import { BusinessActivitiesSummary } from "../../forms/business-activities/2019-2024/BusinessActivitiesSummary";
import { ContactInformationSummary } from "../../forms/contact-information/2019-2024/ContactInformationSummary";
import { CorporateAddressSummary } from "../../forms/corporate-address/2022-2024/CorporateAddressSummary";
import { CorporateMultinationalEnterpriseSummary } from "../../forms/corporate-multinational-enterprise/2019-2024/CorporateMultinationalEnterpriseSummary";
import { EntityDetailsSummary } from "../../forms/EntityDetailsSummary";
import { FinalizeSummary } from "../../forms/finalize/2019-2024/FinalizeSummary";
import { TaxResidentSummary } from "../../forms/tax-resident/2022-2024/TaxResidentSummary";
import { SummaryPage } from "../SummaryPage";

export function STRSummary({ entityDetails }: { entityDetails: any }) {
  const contentBlocks = useMemo(
    () => [
      <div key="title">
        <h1 className="text-xl font-semibold tracking-widest text-blue-500">
          SIMPLIFIED TAX RETURN
        </h1>
      </div>,
      <EntityDetailsSummary key="entity" entityDetails={{ ...entityDetails }} />,
      <AddressOfHeadOfficeSummary key="address" />,
      <ContactInformationSummary key="contact" />,
      <BusinessActivitiesSummary key="activities" />,
      <TaxResidentSummary key="taxResident" />,
      <CorporateAddressSummary key="corporate" />,
      <CorporateMultinationalEnterpriseSummary key="mne" />,
    ].filter(Boolean),
    [entityDetails],
  );
  const contentHeight = 920;
  const { pages, measurementContainer } = usePaginatedContent(contentBlocks, {
    contentHeight,
  });

  return (
    <div>
      {measurementContainer}
      {pages.map((pageContent, i) => {
        const key = (pageContent as React.ReactElement)?.key || `page-${i}`;

        return (
          <SummaryPage key={key} pageNumber={i + 1}>
            {pageContent}
          </SummaryPage>
        );
      })}
      <SummaryPage pageNumber={pages.length + 1}>
        <FinalizeSummary />
      </SummaryPage>
    </div>
  );
}
