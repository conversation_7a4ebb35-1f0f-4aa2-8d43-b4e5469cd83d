import type { LoaderFunctionArgs, TypedDeferredData } from "@remix-run/node";
import type { UIMatch } from "@remix-run/react";
import type { SortingState } from "@tanstack/react-table";
import type { JSX, ReactNode } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogHeader, DialogTrigger, ScrollArea, ScrollBar } from "@netpro/design-system";
import { defer } from "@remix-run/node";
import { Await, Link, useLoaderData, useMatches, useNavigation, useParams } from "@remix-run/react";
import { ChevronLeft } from "lucide-react";
import { Suspense, useState } from "react";
import { EnhancedTable } from "~/components/EnhancedTable";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { PageMessage } from "~/components/ui/PageMessage";
import type { AuditedEntity } from "~/features/audits/api/get-audits";
import { getAudits } from "~/features/audits/api/get-audits";
import { useAuditColumns } from "~/features/audits/hooks/useAuditColumns";
import { useFormatDate } from "~/lib/hooks/useFormatDate";
import { middleware } from "~/lib/middlewares.server";
import { getPaginationParams } from "~/lib/utilities/get-pagination-params";

export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedDeferredData<{
  audits: ReturnType<typeof getAudits>
}>> {
  const { userId, accessToken } = await middleware(["auth"], request);
  const { pageNumber, pageSize } = await getPaginationParams({ request });
  const audits = getAudits({ accessToken, userId, params: { pageNumber, pageSize, entityId: params.id } });

  return defer({ audits });
}

type Breadcrumb = { to: string, label: string };
type Match = UIMatch<unknown, unknown> & {
  handle?: {
    breadcrumb?: Breadcrumb
    returnTo?: ReturnToValue
  }
};

type ReturnToValue = {
  to: string | ((id: string) => string)
  label: string
};

const DEFAULT_RETURN_VALUE = {
  to: "/dashboard",
  label: "Back to Dashboard",
}

export default function LogLayout(): ReactNode {
  const { id } = useParams();
  const formatDate = useFormatDate()
  const matches = useMatches() as Match[];
  const navigation = useNavigation();
  const { audits } = useLoaderData<typeof loader>();
  const [openDetails, setOpenDetails] = useState<boolean>(false);
  const [currentLog, setCurrentLog] = useState<AuditedEntity>();
  const [sorting, setSorting] = useState<SortingState>([])
  const { columns } = useAuditColumns();
  const loading = (state: boolean): JSX.Element => (
    <LoadingState
      isLoading={state}
      className="min-h-96"
      message="Loading history log ..."
    />
  )
  const returnTo = matches[matches.length - 1]?.handle?.returnTo || DEFAULT_RETURN_VALUE;
  const handleOpenDetails = (row: AuditedEntity) => {
    setCurrentLog(row);
    setOpenDetails(true);
  }

  return (
    <div className="p-2 w-full flex flex-col gap-5">
      <Button variant="outline" asChild className="place-self-start pl-2">
        <Link to={typeof returnTo.to === "function" ? returnTo.to(id as string) : returnTo.to}>
          <ChevronLeft className="text-primary ml-0" />
          {returnTo.label}
        </Link>
      </Button>
      <div className="relative">
        <Suspense fallback={loading(navigation.state === "idle")}>
          <Await
            resolve={audits}
            errorElement={(
              <PageMessage title="No audit logs found" subtitle="There are currently no audit logs available." />
            )}
          >
            {(resolvedAudits) => {
              return (
                <>
                  <Dialog modal defaultOpen={false} open={openDetails} onOpenChange={setOpenDetails}>
                    <DialogTrigger />
                    <DialogContent className="max-w-screen-md">
                      <ScrollArea className="max-h-[80vh]">
                        <DialogHeader className="font-semibold text-lg pb-5">{currentLog?.shortDescription}</DialogHeader>
                        <div className="flex flex-col gap-3">
                          {currentLog?.actionDate && (
                            <p className="text-sm">
                              <span className="font-semibold">Logged At:</span>
                              {" "}
                              {formatDate(new Date(currentLog.actionDate), { extend: " hh:mm a" })}
                            </p>
                          )}
                          {currentLog?.action && (
                            <p className="text-sm">
                              <span className="font-semibold">Action:</span>
                              {" "}
                              {currentLog.action}
                            </p>
                          )}
                          {currentLog?.text && (
                            <p className="text-sm">
                              <span className="font-semibold">Description:</span>
                              {" "}
                              {currentLog.text}
                            </p>
                          )}
                        </div>
                        <ScrollBar />
                      </ScrollArea>
                      <DialogFooter>
                        <Button onClick={() => setOpenDetails(false)}>Close</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                  <EnhancedTable
                    size="md"
                    loading={loading(navigation.state === "loading")}
                    onRowClick={row => handleOpenDetails(row.original)}
                    data={resolvedAudits.data}
                    rowId="id"
                    columns={columns}
                    totalItems={resolvedAudits.totalItemCount}
                    reactTableOptions={{
                      onSortingChange: setSorting,
                      state: {
                        sorting,
                      },
                    }}
                  />
                </>
              )
            }}
          </Await>
        </Suspense>
      </div>
    </div>
  );
}
