import type { ShouldRevalidateFunctionArgs } from "@remix-run/react";
import type { ReactNode } from "react";
import { Button, SelectItem } from "@netpro/design-system";
import { json, useLoaderData, useNavigation } from "@remix-run/react";
import { Filter } from "lucide-react";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { PageErrorBoundary } from "~/components/errors/PageErrorBoundary";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormMultiSelect } from "~/components/FormMultiSelect";
import { FormSearch } from "~/components/FormSearch";
import { FormSelect } from "~/components/FormSelect";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { useCompanyColumns } from "~/features/companies/hooks/useCompanyColumns";
import { searchSchema } from "~/features/companies/schemas/searchSchema";
import { useUserHasPermission } from "~/hooks/use-user-has-permission";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import type { OnboardingStatus } from "~/services/api-generated";
import { getCompanies } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Companies Overview",
    to: "/companies/overview",
  },
  title: "Overview",
};

export const onboardingStatuses: OnboardingStatus[] = [
  "Unknown",
  "Onboarding",
  "Approved",
]

export function shouldRevalidate({ currentUrl, nextUrl, defaultShouldRevalidate }: ShouldRevalidateFunctionArgs): boolean {
  if (currentUrl.searchParams.get("columns") !== nextUrl.searchParams.get("columns")) {
    // Do not reload the action or loader data if the visible columns change.
    return false;
  }

  if (nextUrl.pathname !== currentUrl.pathname && nextUrl.pathname.includes(currentUrl.pathname)) {
    // The next URL is a sub-route of the current URL, probably the overlay. Don't reload the data in the background.
    return false;
  }

  return defaultShouldRevalidate;
}

export const loader = makeEnhancedLoader(async ({ request, queryString, enhancedURL }) => {
  await middleware(["auth"], request);
  const schemaData = searchSchema.safeParse(queryString).data
  const { pageSize, pageNumber } = await getFilterParams({ request });
  const { data: paginatedCompanies, error } = await getCompanies({ headers: await authHeaders(request), query: {
    pageSize,
    pageNumber,
    searchTerm: schemaData?.search,
    SortOrder: enhancedURL.searchParams.get("orderDirection") ?? undefined,
    SortBy: enhancedURL.searchParams.get("order") ?? undefined,
    isActive: !schemaData?.state ? undefined : schemaData?.state === "active",
    onboardingStatus: schemaData?.onboardingStatus ?? onboardingStatuses,
  } });

  if (error) {
    throw new Response("Could not retrieve Companies from the database", { status: 412 });
  }

  const defaultOpen = /^\/companies\/overview\/.+\/edit$/.test(enhancedURL.pathname)

  return json({
    paginatedCompanies,
    defaultOpen,
  });
}, { authorize: ["companies.search"] });

export default function Overview(): ReactNode {
  const navigation = useNavigation();
  const { columns } = useCompanyColumns("overview");
  const canViewCompany = useUserHasPermission({ oneOf: ["companies.view"] });
  const onRowClick = canViewCompany ? {} : { onRowClick: () => false };
  const { paginatedCompanies: { data, totalItemCount }, defaultOpen } = useLoaderData<typeof loader>();
  const { formMethods } = useFilterForm(searchSchema);

  return (
    <CardContainer>
      <Form formMethods={formMethods}>
        <FilterRow>
          <FormColumnsFilter label="Visible Columns" columns={columns} />
          <FormSelect
            name="state"
            label="PCP Entity Status"
            selectValueProps={{ placeholder: "All" }}
            options={["All", "Active", "Inactive"].map(state => (
              <SelectItem key={state.toLowerCase()} value={state.toLowerCase()}>{state}</SelectItem>
            ))}
          />
          <FormMultiSelect
            name="onboardingStatus"
            label="Onboarding Status"
            triggerLabel="All"
            options={onboardingStatuses.map(status => ({ checked: false, key: status, label: status }))}
          />
        </FilterRow>
        <FilterRow>
          <div className="col-span-full flex flex-row items-center gap-2">
            <FormSearch name="search" formItemProps={{ className: "w-full" }} inputProps={{ placeholder: "Search entity name, master client code, jurisdiction, entity name, vp etc." }} />
            <Button size="sm" className="gap-1.5" type="submit">
              <Filter size={14} />
              Apply Filter(s)
            </Button>
          </div>
        </FilterRow>
      </Form>

      <EnhancedTableContainer>
        <EnhancedTable
          {...onRowClick}
          rowId="id"
          columns={columns}
          data={data}
          returnURL="/companies/overview"
          totalItems={totalItemCount}
          sheetURL={row => `/companies/overview/${row.id}/edit`}
          loading={<LoadingState isLoading={navigation.state === "loading"} />}
          defaultOpen={defaultOpen}
        />
      </EnhancedTableContainer>
    </CardContainer>
  );
}

export const ErrorBoundary = PageErrorBoundary;
