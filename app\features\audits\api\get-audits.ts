import type { PaginatedResult } from "~/features/master-clients/types";
import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

/**
 * Example Audit object:
 *
 * {
 *       "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
 *       "changedAt": "2024-11-04T20:16:39.598Z",
 *       "changedBy": "string",
 *       "contextId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
 *       "entities": [
 *         {
 *           "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
 *           "entityName": "string",
 *           "entityId": "string",
 *           "action": "string",
 *           "details": [
 *             {
 *               "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
 *               "column": "string",
 *               "oldValue": "string",
 *               "newValue": "string"
 *             }
 *           ]
 *         }
 *       ]
 *     }
 *
 *  API endpoint query params:
 *
 *  - fromDate string format: yyyy-mm-dd
 *  - toDate string format: yyyy-mm-dd
 *  - entityId string format: uuid
 *  - pageNumber: number format: int
 *  - pageSize: number format: int
 */

export type AuditLogEntityDetails = {
  id: string // UUID
  column: string
  oldValue: string
  newValue: string
}

export type AuditLogEntity = {
  id: string // UUID
  entityName: string
  entityId: string
  action: string
  details: AuditLogEntityDetails[]
}

export type AuditLog = {
  id: string // UUID
  changedAt: string
  changedBy: string
  contextId: string // UUID
  entities: AuditLogEntity[]
}

export type AuditedEntity = {
  id: string // UUID
  actionDate: string
  userName: string
  emailAddress: string
  activityType: string
  action: string
  shortDescription: string
  text: string
  entityName: string
  audits: AuditLog[]
}

type Params = {
  params: {
    pageNumber?: number
    pageSize?: number
    fromDate?: string
    toDate?: string
    entityId?: string
  }
}

export async function getAudits({ userId, accessToken, params }: ClientRequestHeaders & Params): Promise<PaginatedResult<AuditedEntity>> {
  /*
   * Overwrite the entityId for testing purposes, as long as the Users view does not implement the UUID entity ID yet
   * params.entityId = "4FCF5353-27B2-4195-9F4A-3E6BB38EB506"
   */
  return client.get<PaginatedResult<AuditedEntity>>(
    "/audits/activitylogs",
    accessToken,
    userId,
    { params },
  );
}
