import type { ReactNode } from "react";
import { But<PERSON> } from "@netpro/design-system";
import { Form, useFormAction, useNavigate, useParams } from "@remix-run/react";
import * as AlertDialog from "~/components/AlertDialog";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { resetMfaMethod } from "~/services/api-generated";

export const action = makeEnhancedAction(async ({ setNotification, redirect, params, request }) => {
  const { id } = params
  await middleware(["auth"], request);
  const { error } = await resetMfaMethod({ headers: await authHeaders(request), path: { userId: id! } })

  if (error) {
    setNotification({ title: "Failed to reset two factor authentication", variant: "error" })
  } else {
    setNotification({ title: "Two factor authentication has been reset" })
  }

  return redirect(`/users/${id}`)
}, { authorize: ["users.reset-authentication"] });

export const loader = makeEnhancedLoader(() => {
  return null
}, { authorize: ["users.reset-authentication"] })

export default function UseReset2FA(): ReactNode {
  const params = useParams();
  const navigate = useNavigate()
  const formAction = useFormAction();

  return (
    <AlertDialog.Root open onOpenChange={() => navigate(`/users/${params.id}`)}>
      <AlertDialog.Portal>
        <AlertDialog.Overlay />
        <AlertDialog.Content>
          <AlertDialog.Title>
            Are you sure you want to reset the 2FA for this user?
          </AlertDialog.Title>
          <AlertDialog.Description>
            Clicking confirm will reset the Two Factor Authentication for this user.
          </AlertDialog.Description>
          <AlertDialog.Footer>
            <AlertDialog.Cancel asChild>
              <Button onClick={() => navigate("/users/")} variant="outline" size="sm">
                Cancel
              </Button>
            </AlertDialog.Cancel>
            <Form action={formAction} method="POST">
              <Button type="submit" size="sm">Confirm</Button>
            </Form>
          </AlertDialog.Footer>
        </AlertDialog.Content>
      </AlertDialog.Portal>
    </AlertDialog.Root>
  )
}
