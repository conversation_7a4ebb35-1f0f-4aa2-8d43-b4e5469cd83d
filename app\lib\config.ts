import { version } from "package.json";

export function defaultMeta(title?: string): Record<string, string>[] {
  return [
    { title: title || "Trident Trust - Private Client Management Portal" },
    { name: "description", content: "Access the secure Trident Trust Private Client Management Portal. Manage submissions, update beneficial owners and directors." },
    { name: "version", content: version },
  ]
}

export const defaultLinks = [
  { rel: "apple-touch-icon", sizes: "120x120", href: "/images/apple-touch-icon.png" },
  { rel: "icon", sizes: "32x32", href: "/images/favicon-32x32.png" },
  { rel: "icon", sizes: "16x16", href: "/images/favicon-16x16.png" },
  { rel: "mask-icon", color: "#5bbad5", href: "/images/safari-pinned-tab.svg" },
];

export function defaultHeaders() {
  return {
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
    "X-Content-Type-Options": "nosniff",
    "X-XSS-Protection": "1; mode=block",
    "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0",
  };
}
