import type { <PERSON>actN<PERSON>, TdHTMLAttributes } from "react";
import { cn, TableCell } from "@netpro/design-system";

type SummaryTableCellProps = {
  label: string
  value?: string
} & TdHTMLAttributes<HTMLTableCellElement>;

export function SummaryTableCell({ label, value, className, ...props }: SummaryTableCellProps): ReactNode {
  return (
    <TableCell className={cn("last:border-0 py-1", className)} {...props}>
      <p>
        {label}
        {": "}
        <span className="font-semibold">{value ?? ""}</span>
      </p>
    </TableCell>
  );
}
