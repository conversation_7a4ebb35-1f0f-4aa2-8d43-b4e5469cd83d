import type { ReactNode } from "react";
import { Badge } from "@netpro/design-system";
// eslint-disable-next-line no-restricted-imports -- Specific formatting required in this case
import { format } from "date-fns";
import type { SyncDetailsDTO } from "~/services/api-generated";

type Props = {
  entityName: string
  syncDetails: SyncDetailsDTO
}

export function SynchronizationStatusCard({ entityName, syncDetails }: Props): ReactNode {
  return (
    <div className="flex items-center space-x-3 py-2.5 pl-4 pr-8 ring-1 ring-gray-200 rounded-md ">
      <div>
        <p className="text-xl font-semibold text-gray-800 mb-2">{entityName}</p>
        <p className="text-sm font-semibold text-gray-600">
          Last successful sync was at:
          {" "}
          <span className="font-bold">
            {syncDetails?.lastSuccessfulSync ? format(syncDetails?.lastSuccessfulSync, "dd-MMMM-yyyy HH:mm OOO").toUpperCase() : "N/A"}
          </span>
        </p>
        <ul className="list-disc list-inside text-sm font-semibold text-gray-600 mt-1">
          <li>
            Updated records:
            {syncDetails?.updatedCount
              ? (
                  <Badge variant="success" className="ml-1">
                    {syncDetails?.updatedCount}
                  </Badge>
                )
              : (
                  <Badge variant="secondary" className="ml-1">
                    0
                  </Badge>
                )}
          </li>
          <li>
            Deleted records:
            {syncDetails?.deletedCount
              ? (
                  <Badge variant="danger" className="ml-1">
                    {syncDetails?.deletedCount}
                  </Badge>
                )
              : (
                  <Badge variant="secondary" className="ml-1">
                    0
                  </Badge>
                )}
          </li>
        </ul>
        <p className="text-sm font-semibold text-gray-700 mt-3 mb-1">Jurisdictions included:</p>
        <div className="flex">
          {syncDetails?.jurisdictionsUsed?.map(jurisdiction => (
            <Badge key={jurisdiction} variant="info" className="mr-1">
              {jurisdiction}
            </Badge>
          ))}
        </div>
      </div>
    </div>
  );
}
