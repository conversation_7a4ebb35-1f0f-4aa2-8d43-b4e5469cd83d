# Temporary disable while tests aren't implemented
trigger: none

# trigger: 
#   - main
#   - feat/*
#   - fix/*
#   - develop

pool:
  vmImage: ubuntu-latest

jobs:
  - job: 'Lint'
    displayName: 'Playwright tests'
    steps:
      - checkout: self
        clean: true
        persistCredentials: true
        displayName: 'Checkout repository'

      - task: NodeTool@0
        inputs:
          versionSource: 'fromFile'
          versionFilePath: './.nvmrc'
          checkLatest: true
        displayName: 'Install Node.js'

      - task: Npm@1
        inputs:
          command: 'install'
          workingDir: '.'
          verbose: true
        displayName: 'Install dependencies'

      - task: Npm@1
        inputs:
          command: 'custom'
          workingDir: '.'
          customCommand: 'run test'
        displayName: 'Run Playwright tests'