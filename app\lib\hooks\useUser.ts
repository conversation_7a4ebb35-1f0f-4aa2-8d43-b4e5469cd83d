import { useRouteLoaderData } from "@remix-run/react";
import type { RootLoaderData } from "~/lib/auth/types/session-type";

export type ClientSideUser = {
  id?: string
  name: string
  email: string
}
export function useUser(): ClientSideUser {
  const data = useRouteLoaderData<RootLoaderData>("root")
  const name = data?.user.name ?? "";
  const email = data?.user.email ?? "";

  return { name, email };
}
