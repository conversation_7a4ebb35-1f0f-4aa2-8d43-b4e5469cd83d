import type { J<PERSON><PERSON> } from "react";
import { <PERSON><PERSON> } from "@netpro/design-system";
import { Link, Outlet, redirect, useLoaderData, useParams } from "@remix-run/react";
import { Ban, Check, RotateCcw, ScrollText } from "lucide-react";
import { useForm } from "react-hook-form";
import { ActionSheetActionRow } from "~/components/ActionSheetActionRow";
import { ActionSheetBody } from "~/components/ActionSheetBody";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetDescriptionList } from "~/components/ActionSheetDescriptionList";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { ActionSheetSection } from "~/components/ActionSheetSection";
import { Authorized } from "~/components/Authorized";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementGetUser } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, params, json, setNotification }) => {
  await middleware(["auth"], request);
  const userResponse = await managementGetUser({ headers: await authHeaders(request), path: { id: params.id! } })

  if (!userResponse.data) {
    setNotification({ title: "The requested user could not be found", variant: "error" })

    return redirect("/users/")
  }

  return json({ user: userResponse.data })
}, { authorize: ["users.view"] });

export default function UserDetail(): JSX.Element {
  const params = useParams();
  const formMethods = useForm();
  const { user } = useLoaderData<typeof loader>()

  return (
    <>
      <ActionSheetBody formMethods={formMethods}>
        <ActionSheetContent title="User Details">
          <ActionSheetSection title="Details" collapsible>
            <ActionSheetDescriptionList
              data={user}
              headers={[
                ["email", "Email"],
                ["primaryRoleLabel", "Type"],
              ]}
            />
          </ActionSheetSection>
          <ActionSheetSection title="Actions">
            {user.isBlocked && (
              <Authorized oneOf={["users.unblock"]}>
                <ActionSheetActionRow label="Unblock User">
                  <Button asChild type="button" variant="outline" size="sm" className="self-center text-sm flex items-center justify-center gap-1.5">
                    <Link to="./unblock">
                      <Check size={14} className="text-green-600" />
                      <span className="text-xs font-semibold">Unblock</span>
                    </Link>
                  </Button>
                </ActionSheetActionRow>
              </Authorized>
            )}
            {!user.isBlocked && (
              <Authorized oneOf={["users.block"]}>
                <ActionSheetActionRow label="Block User">
                  <Button asChild type="button" variant="outline" size="sm" className="self-center text-sm flex items-center justify-center gap-1.5">
                    <Link to="./block">
                      <Ban size={14} className="text-red-600" />
                      <span className="text-xs font-semibold">Block</span>
                    </Link>
                  </Button>
                </ActionSheetActionRow>
              </Authorized>
            )}
            {(user.roleNames?.includes("Client") || user.primaryRoleLabel === "Client portal user") && (
              <Authorized oneOf={["users.reset-authentication"]}>
                <ActionSheetActionRow label="Reset 2FA">
                  <Button asChild type="button" variant="outline" size="sm" className="self-center text-sm flex items-center justify-center gap-1.5">
                    <Link to="./reset-2fa">
                      <RotateCcw size={14} className="text-blue-600" />
                      <span className="text-xs font-semibold">Reset</span>
                    </Link>
                  </Button>
                </ActionSheetActionRow>
              </Authorized>
            )}
            <Authorized oneOf={["users.view-log"]}>
              <ActionSheetActionRow label="User History">
                <Button asChild size="sm" variant="outline" className="self-center text-sm flex items-center justify-center gap-1.5">
                  <Link to={`/users/${params.id}/log`}>
                    <ScrollText size={14} className="text-blue-600" />
                    <span className="text-xs font-semibold">View Log</span>
                  </Link>
                </Button>
              </ActionSheetActionRow>
            </Authorized>
          </ActionSheetSection>
        </ActionSheetContent>
        <ActionSheetFooter />
      </ActionSheetBody>
      <Outlet />
    </>
  )
}
