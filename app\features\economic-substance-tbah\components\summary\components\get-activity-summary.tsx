import type { RelevantActivityDeclarationSchemaType } from "~/features/economic-substance-tbah/types/relevant-activity-declaration-schema";
import { Pages, type PageSlug } from "~/features/economic-substance-tbah/utilities/form-pages";
import { BaseActivities } from "./relevant-activities/BaseActivities";
import { BusinessActivities } from "./relevant-activities/BusinessActivities";

export function getActivitySummary(activity: NonNullable<RelevantActivityDeclarationSchemaType["relevantActivities"]>[0]) {
  if (!activity) {
    throw new Error("An activity is required to retrieve the summary")
  }

  const { page } = activity

  return (
    <>
      <BaseActivities label={activity.label} page={page as PageSlug} />
      {page !== Pages.HOLDING_BUSINESS && (
        <BusinessActivities page={page as PageSlug} />
      )}

    </>
  )
}
