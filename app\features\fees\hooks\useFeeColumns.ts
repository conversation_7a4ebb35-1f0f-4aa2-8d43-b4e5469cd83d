import type { DisplayColumnDef } from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { useMemo } from "react";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import type { STRLatePaymentFeeDTO } from "~/services/api-generated";

type Type = "submissions" | "late-fee-management" | "fee-settings"

/**
 * This is a helper hook for the table column definitions. Currently, the definitions are shared between
 * the overview and pending onboardings pages. However, using this hook, we're able to easily change columns
 * between these views in the future.
 * @param type
 */
export function useFeeColumns(type: Type): { columnsLateFee: DisplayColumnDef<STRLatePaymentFeeDTO>[] } {
  const formatColDate = useFormatColDate()
  const columnHelper = createColumnHelper<STRLatePaymentFeeDTO>();
  let columnsLateFee = [];

  switch (type) {
    case "submissions":
    case "late-fee-management":
    default:
      columnsLateFee = useMemo(() => [
        columnHelper.display({
          id: "financialYear",
          header: "Financial Year",
          cell: props => props.row.original?.financialYear,
        }),
        columnHelper.display({
          id: "startAt",
          header: "Fee Start Date",
          cell: formatColDate("startAt", { fallback: "N/A" }),
        }),
        columnHelper.display({
          id: "endAt",
          header: "Fee End Date",
          cell: formatColDate("endAt", { fallback: "N/A" }),

        }),
        columnHelper.display({
          id: "amount",
          header: "Late Fee",
          cell: props => props.row.original.amount,
        }),
      ], [columnHelper, formatColDate]);
      break;
  }

  return { columnsLateFee }
}
