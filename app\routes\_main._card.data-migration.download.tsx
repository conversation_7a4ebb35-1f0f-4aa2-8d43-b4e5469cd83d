import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { getMigrationLogWorkbook } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request }) => {
  const workbookResponse = await getMigrationLogWorkbook({ headers: await authHeaders(request) });

  if (!workbookResponse.data) {
    return new Response(null, { status: 404, statusText: "Not Found" })
  }

  return new Response(workbookResponse.data, {
    headers: workbookResponse.response.headers,
  });
}, { authorize: ["str.data-migration"] })
