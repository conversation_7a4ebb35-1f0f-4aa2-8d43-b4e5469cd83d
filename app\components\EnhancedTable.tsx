import type { AccessorKeyColumnDef, ColumnDef, DisplayColumnDef, GroupColumnDef, Row, RowData, SortingState, TableOptions } from "@tanstack/react-table";
import type { ComponentProps, JSX, ReactNode } from "react";
import {
  Button,
  ScrollArea,
  ScrollBar,
  Sheet,
  SheetContent,
  SheetPortal,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@netpro/design-system";
import { Outlet, useLocation, useNavigate, useSearchParams } from "@remix-run/react";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import clsx from "clsx";
import { ArrowDownAZ, ArrowDownUp, ArrowUpAZ, RotateCcwIcon } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Pagination } from "~/components/ui/filters/Pagination";
import { TableOverlayMessage } from "~/components/ui/filters/TableOverlayMessage";
import { useQueryString } from "~/hooks/use-query-string";
import { usePaginationParams } from "~/lib/hooks/usePaginationParams";
import type { MakeOptional } from "~/lib/types/MakeOptional";
import { ActionSheetContext } from "./ActionSheetContext";

type Props<TData extends RowData> = {
  data?: TData[] | null
  rowId: keyof TData
  defaultOpen?: boolean
  columns: (ColumnDef<TData> | DisplayColumnDef<TData> | AccessorKeyColumnDef<TData, any> | GroupColumnDef<TData, any>)[]
  reactTableOptions?: MakeOptional< TableOptions<TData>, "data" | "getCoreRowModel" | "columns">
  sheetURL?: string | ((row: Row<TData>) => string)
  totalItems?: number
  onRowClick?: (row: Row<TData>) => void
  loading?: ReactNode
  returnURL?: string
  size?: "xs" | "md" | "normal"
  showPagination?: boolean
}

/**
 * EnhancedTable is a versatile table component built on top of `@tanstack/react-table` and integrates with Remix.
 * It provides features like pagination, sorting, customizable column rendering, and row click handling with optional sheets.
 *
 * @template TData - The type of the data to be displayed in the table.
 *
 * @param {object} props - The props for the EnhancedTable component.
 * @param {TData[] | null} [props.data] - The data to be displayed in the table. Defaults to an empty array.
 * @param {keyof TData} props.rowId - The key used as the unique identifier for table rows.
 * @param {ColumnDef<TData>[]} props.columns - The column definitions for the table.
 * @param {boolean} [props.defaultOpen] - Whether the sheet should be open by default.
 * @param {MakeOptional<TableOptions<TData>, "data" | "getCoreRowModel" | "columns">} [props.reactTableOptions] - Additional options for the React Table instance.
 * @param {string | ((row: Row<TData>) => string)} [props.sheetURL] - The URL or function to generate a URL for the sheet when a row is clicked.
 * @param {number} [props.totalItems] - The total number of items in the dataset, used for pagination.
 * @param {(row: Row<TData>) => void} [props.onRowClick] - A custom handler for row clicks. Overrides `sheetURL` behavior if provided.
 * @param {ReactNode} [props.loading] - A loading indicator to display when the table is loading data.
 * @param {string} [props.returnURL] - The URL to navigate to when the sheet is closed. Defaults to navigating back.
 * @param {"xs" | "md" | "normal"} [props.size] - The size of the table cells. Determines padding and spacing.
 * @param {boolean} props.showPagination - Option to force the hiding of the pagination component
 *
 * @returns {JSX.Element} A fully functional table with pagination, sorting, and row-level interactions.
 *
 * @example
 * // Example usage of the EnhancedTable component
 * import { EnhancedTable } from '~/components/EnhancedTable';
 * import { z } from 'zod';
 * import { useFilterForm } from '~/hooks/useFilterForm';
 *
 * const columns = [
 *   { id: 'name', header: 'Name', accessorKey: 'name' },
 *   { id: 'age', header: 'Age', accessorKey: 'age' },
 * ];
 *
 * const data = [
 *   { id: 1, name: 'John Doe', age: 25 },
 *   { id: 2, name: 'Jane Smith', age: 30 },
 * ];
 *
 * function MyEnhancedTable() {
 *   const schema = z.object({
 *     columns: z.array(z.string()).optional(),
 *   });
 *   const { formMethods } = useFilterForm(schema);
 *
 *   return (
 *     <EnhancedTable
 *       data={data}
 *       rowId="id"
 *       columns={columns}
 *       totalItems={data.length}
 *       sheetURL={(row) => `/details/${row.id}`}
 *       size="md"
 *     />
 *   );
 * }
 */
export function EnhancedTable<TData,>({ reactTableOptions, size = "normal", data, rowId, columns, sheetURL, onRowClick, totalItems, loading, defaultOpen, returnURL, showPagination = true }: Props<TData>): JSX.Element {
  const queryString = useQueryString();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [previousSearch, setPreviousSearch] = useState("");
  const { pageSize, order, orderDirection } = usePaginationParams();
  const [sorting, setSorting] = useState<SortingState>([
    ...(order != null ? [{ id: order, desc: orderDirection === "desc" }] : []),
  ]);
  const [isSheetOpen, setIsSheetOpen] = useState(defaultOpen)
  const [sheetContainer, setSheetContainer] = useState<Element | null>(null);

  useEffect(() => {
    setSheetContainer(document.body);
  }, []);

  useEffect(() => {
    setIsSheetOpen(defaultOpen)
  }, [defaultOpen]);

  const { state, ...restReactTableOptions } = reactTableOptions || {}
  const table = useReactTable({
    data: data || [],
    columns: queryString.columns ? columns.filter(column => column.id && queryString.columns?.includes(column.id)) : columns,
    state: { sorting, ...state },
    manualSorting: true,
    manualPagination: true,
    rowCount: Number(pageSize),
    getCoreRowModel: getCoreRowModel(),
    getRowId: row => row[rowId] as string,
    onSortingChange(updater) {
      const newSorting = typeof updater === "function" ? updater(sorting) : updater;

      setSorting(newSorting);

      const newParams = new URLSearchParams(location.search);

      if (newSorting[0]) {
        newParams.set("order", newSorting[0].id);
        newParams.set("orderDirection", newSorting[0].desc ? "desc" : "asc");
      } else {
        newParams.delete("order");
        newParams.delete("orderDirection");
      }

      navigate({ search: newParams.toString() });
    },
    sortDescFirst: false,
    ...restReactTableOptions,
  });
  //
  const handleRowClick = (row: Row<TData>): void => {
    if (onRowClick) {
      onRowClick(row)

      return
    }

    if (!sheetURL) {
      return
    }

    const url = typeof sheetURL === "string" ? sheetURL + row.id : sheetURL(row)
    const searchParams = new URLSearchParams(location.search);
    const currentSearch = searchParams.toString();
    setPreviousSearch(currentSearch);
    // Preserve table search params params when sheet open to keep same items in list in the background
    navigate(`${url}?${currentSearch}`);
    setIsSheetOpen(true);
  }
  //
  const handleSheetClose = useCallback((): void => {
    setIsSheetOpen(false);
    if (returnURL) {
      navigate(`${returnURL}?${previousSearch}`)
    } else {
      // This doesn't work when a modal is opened in a sub-route (e.g. /users/1/block). Please enforce the onCloseURL prop in such cases.
      navigate(-1)
    }
  }, [navigate, returnURL, previousSearch]);
  //
  const handleOnInteractOutside: ComponentProps<typeof SheetContent>["onInteractOutside"] = (e) => {
    if (e.target === null) {
      return;
    }

    let steps = 0;
    const searchLimit = 10;
    let current: Node | null = e.target as HTMLDivElement;

    while (current && steps < searchLimit) {
      if (!("dataset" in current)) {
        break
      }

      if ("dataset" in current && current.dataset) {
        const keys = Object.keys(current.dataset);

        if (keys.includes("sonnerToast") || keys.includes("sonnerToaster")) {
          return e.preventDefault()
        }
      }

      current = current.parentElement;
      steps++;
    }
  }
  //
  const actionSheetContextValue = useMemo(
    () => ({ closeSheet: handleSheetClose }),
    [handleSheetClose],
  )

  return (
    <div className="flex flex-col gap-4">
      <div className="relative">
        {loading}
        {totalItems === 0 && (
          <TableOverlayMessage className="min-h-96 flex flex-col gap-4">
            <div className="flex flex-col items-center">
              <h2 className="font-semibold text-xl">No records found</h2>
              {searchParams.size > 0 && <p>The applied filters did not return any records.</p>}
            </div>

            {searchParams.size > 0 && (
              <Button size="sm" className="gap-1 mt-2" onClick={() => setSearchParams(new URLSearchParams())}>
                <RotateCcwIcon size={16} />
                Reset filters
              </Button>
            )}
          </TableOverlayMessage>
        )}
        {Number(totalItems) > 0 && (
          <ScrollArea>
            <Table>
              <TableHeader className="[&_tr]:border-b [&_tr]:border-b-teal-200">
                {table.getHeaderGroups().map(headerGroup => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <TableHead
                        onClick={header.column.getToggleSortingHandler()}
                        title={
                          header.column.getCanSort()
                            ? header.column.getNextSortingOrder() === "asc"
                              ? "Sort ascending"
                              : header.column.getNextSortingOrder() === "desc"
                                ? "Sort descending"
                                : "Clear sort"
                            : undefined
                        }
                        className={clsx(
                          "px-6 py-3 font-semibold text-black whitespace-nowrap hover:bg-teal-100 hover:text-teal-900 transition-colors duration-150",
                          {
                            "px-2 py-1": size === "xs",
                            "px-4 py-2": size === "md",
                          },
                        )}
                        style={{
                          width: `${header.getSize()}px`,
                        }}
                        key={header.id}
                      >
                        <div className="flex gap-x-4 items-center">
                          {!header.isPlaceholder && flexRender(header.column.columnDef.header, header.getContext())}
                          {header.column.getCanSort() && !header.column.getIsSorted() && <ArrowDownUp size={16} />}
                          {header.column.getIsSorted() === "asc" && <ArrowDownAZ size={16} /> }
                          {header.column.getIsSorted() === "desc" && <ArrowUpAZ size={16} /> }
                        </div>
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getPaginationRowModel().rows.map(row => (
                  <TableRow
                    className={clsx({
                      "cursor-pointer": typeof onRowClick === "function" || Boolean(sheetURL),
                      "opacity-50 bg-gray-50": (row.original as any).isDeleted,
                    }, "hover:bg-teal-100 hover:text-teal-900 transition-colors duration-150")}
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    onClick={() => handleRowClick(row)}
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell
                        className={clsx(
                          "px-6 py-3 whitespace-nowrap",
                          {
                            "px-2 py-1": size === "xs",
                            "px-4 py-2": size === "md",
                            "text-gray-500": (row.original as any).isDeleted,
                          },
                        )}
                        key={cell.id}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        )}
      </div>
      {Number(totalItems) > 0 && showPagination && (
        <Pagination totalItems={totalItems!} />
      )}

      <ActionSheetContext.Provider value={actionSheetContextValue}>
        <Sheet
          open={isSheetOpen}
          onOpenChange={handleSheetClose}
        >
          <SheetPortal container={sheetContainer}>
            <SheetContent
              side="right"
              className="w-[400px] sm:w-[540px] [&>button]:hidden"
              onInteractOutside={handleOnInteractOutside}
            >
              <Outlet />
            </SheetContent>
          </SheetPortal>
        </Sheet>
      </ActionSheetContext.Provider>
    </div>
  )
}
