import type { PageSlug } from "../utilities/form-pages";
import { z } from "zod";
import { nonNullDate, preprocessArray, stringBoolean } from "~/lib/utilities/zod-validators";
import { Pages } from "../utilities/form-pages";

const relevantActivitySchema = z.object({
  id: z.string(),
  label: z.string(),
  page: z.string().nullish(),
  selected: stringBoolean(),
  carriedOnForOnlyPartOfFinancialPeriod: stringBoolean().nullish(),
  startDate: nonNullDate("Start date").nullish(),
  endDate: nonNullDate("End date").nullish(),
}).refine((data) => {
  if (data.selected === "true") {
    return data.carriedOnForOnlyPartOfFinancialPeriod !== null
  }

  return true;
}, {
  message: "Required",
  path: ["carriedOnForOnlyPartOfFinancialPeriod"],
}).refine((data) => {
  if (data.selected === "true") {
    return data.startDate !== null
  }

  return true;
}, {
  message: "Required",
  path: ["startDate"],
}).refine((data) => {
  if (data.selected === "true") {
    return data.endDate !== null
  }

  return true;
}, {
  message: "Required",
  path: ["endDate"],
});

export const relevantActivityDeclarationSchema = z.object({
  relevantActivities: preprocessArray(z.array(relevantActivitySchema)).optional(),
})
  .refine((data) => {
    // Validate that at least one activity is selected
    return data.relevantActivities?.some(item => item.selected === "true");
  }, {
    path: ["relevantActivities", 0, "selected"],
    message: "At least one option must be selected.",

  })

export type RelevantActivityDeclarationSchemaType = z.infer<typeof relevantActivityDeclarationSchema>;

const relevantActivities: { id: string, label: string, page: PageSlug | null }[] = [
  { id: "none", label: "None", page: null },
  { id: "holdingBusiness", label: "Holding Business", page: Pages.HOLDING_BUSINESS },
  { id: "financeAndLeasingBusiness", label: "Finance and Leasing Business", page: Pages.FINANCE_LEASING_BUSINESS },
  { id: "bankingBusiness", label: "Banking Business", page: Pages.BANKING_BUSINESS },
  { id: "insuranceBusiness", label: "Insurance Business", page: Pages.INSURANCE_BUSINESS },
  { id: "fundManagementBusiness", label: "Fund Management Business", page: Pages.FUND_MANAGEMENT_BUSINESS },
  { id: "headquartersBusiness", label: "Headquarters Business", page: Pages.HEADQUARTERS_BUSINESS },
  { id: "shippingBusiness", label: "Shipping Business", page: Pages.SHIPPING_BUSINESS },
  { id: "intellectualPropertyBusiness", label: "Intellectual Propertry Business", page: Pages.INTELLECTUAL_PROPERTY_BUSINESS },
  { id: "distributionAndServiceCentreBusiness", label: "Distribution and Service Centre Business", page: Pages.DISTRIBUTION_SERVICE_CENTRE_BUSINESS },
]

export const defaultValues: RelevantActivityDeclarationSchemaType = {
  relevantActivities: relevantActivities?.map(activity => ({
    ...activity,
    selected: "false",
    carriedOnForOnlyPartOfFinancialPeriod: null,
    startDate: null,
    endDate: null,
  })),
};
