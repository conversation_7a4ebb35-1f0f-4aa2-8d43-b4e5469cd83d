import { z } from "zod";
import { optionalEnumWithEmpty } from "~/lib/optionalEnumWithEmpty";

function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

function preprocessDate(value: unknown): string | undefined {
  if (typeof value === "string" && !Number.isNaN(Date.parse(value))) {
    return formatDate(new Date(value)); // Valid date, format it
  }

  if (value instanceof Date && !Number.isNaN(value.getTime())) {
    return formatDate(new Date(value)); // Valid date, format it
  }

  return undefined;
}

export const searchSchema = z.object({
  SearchTerm: z.string().optional(),
  columns: z.string().array().optional(),
  page: z.coerce.number().optional(),
  ConfirmedDateTo: z.preprocess(preprocessDate, z.string().optional()),
  ConfirmedDateFrom: z.preprocess(preprocessDate, z.string().optional()),
  Position: optionalEnumWithEmpty(["Director", "BeneficialOwner"]),
  ProductionOffice: optionalEnumWithEmpty(["TBVI", "THKO", "TNEV", "TPANVG", "TCYP"]),
  Specifics: z.enum(["NoBoDirInformation", "BoDirInformation", "MissingInformation"]).array().optional(),
  DataStatuses: z.enum(["Confirmed", "Initial", "PendingUpdateRequest", "Refreshed", "Subsequent"]).array().optional(),
});

export type SearchSchema = z.infer<typeof searchSchema>
