import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

type declineCompanyParams = {
  companyId: string
  data: {
    declineReason: string
  }
};

export async function declineCompany({
  companyId,
  accessToken,
  userId,
  data,
}: declineCompanyParams & ClientRequestHeaders): Promise<null> {
  return client.post<null>(
    `/management/companies/${companyId}/decline`,
    accessToken,
    userId,
    data,
  );
}
