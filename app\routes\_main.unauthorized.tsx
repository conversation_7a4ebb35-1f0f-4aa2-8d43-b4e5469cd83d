import type { ReactNode } from "react";
import { <PERSON><PERSON> } from "@netpro/design-system";
import { <PERSON> } from "@remix-run/react";
import { ChevronLeft, OctagonMinus } from "lucide-react";
import { PageMessage } from "~/components/ui/PageMessage";

export default function Unauthorized(): ReactNode {
  return (
    <PageMessage title="Forbidden" subtitle="You are not authorized to view this page." IconComponent={OctagonMinus}>
      <Button variant="outline" size="sm" className="gap-1.5" asChild>
        <Link to="/dashboard">
          <ChevronLeft className="text-primary" />
          Return to dashboard
        </Link>
      </Button>
    </PageMessage>
  )
}
