import type { DisplayColumnDef } from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { useMemo } from "react";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import type { ListSubmissionDTO } from "~/services/api-generated";

export function useBfrColumns(): { columns: DisplayColumnDef<ListSubmissionDTO>[] } {
  const formatDateColDate = useFormatColDate()
  const columnHelper = createColumnHelper<ListSubmissionDTO>();
  let columns = [];

  columns = useMemo(() => {
    return [
      columnHelper.display({
        id: "email",
        header: "Use Accounting Records?",
        cell: (props) => {
          return (
            <div>
              {props.row.original.isUsingAccountingRecordsTool === "true" ? "Yes" : "No"}
            </div>
          )
        },
      }),
      columnHelper.display({
        id: "endsAt",
        header: "Financial Period End Date",
        cell: formatDateColDate("financialPeriodEndsAt"),
      }),
      columnHelper.display({
        id: "name",
        header: "Entity Name",
        cell: props => props.row.original.name,
      }),
      columnHelper.display({
        id: "entityNumber",
        header: "Regulatory Code",
        cell: props => props.row.original.legalEntityCode,
      }),
      columnHelper.display({
        id: "masterClientCode",
        header: "Master Client Code",
        cell: props => props.row.original.masterClientCode,
      }),
      columnHelper.display({
        id: "companyNumber",
        header: "VP Code",
        cell: props => props.row.original.legalEntityCode,
      }),
      columnHelper.display({
        id: "status",
        header: "Status",
        cell: props => props.row.original.status,
      }),
      columnHelper.display({
        id: "created",
        header: "Created Date",
        cell: formatDateColDate("createdAt"),
      }),
    ]
  }, [columnHelper, formatDateColDate]);

  return { columns }
}
