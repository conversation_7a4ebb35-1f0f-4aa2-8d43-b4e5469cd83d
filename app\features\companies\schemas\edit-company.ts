import { z } from "zod";

export const editCompanySchema = z.object({
  strSubmissionFee: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined) {
      return null;
    }

    return Number(val);
  }, z.number().int({
    message: "Submission fee must be an integer",
  }).max(9999, {
    message: "Submission fee must be less than 10000",
  }).nonnegative("Submission fee must be a positive number")
    .optional()
    .nullable()),
  latePaymentException: z.enum(["true", "false"]).optional(),
  strModule: z.boolean(),
  boDirModule: z.boolean(),
  bfrModule: z.boolean(),
  esBahamasModule: z.boolean(),
  annualFees: z.array(z.object({
    financialYear: z.number().int(),
    isPaid: z.boolean(),
  })).nullable().optional(),
  bfrSubmissionFee: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined) {
      return null;
    }

    return Number(val);
  }, z.number().int({
    message: "Submission fee must be an integer",
  }).max(9999, {
    message: "Submission fee must be less than 10000",
  }).nonnegative("Submission fee must be a positive number")
    .optional()
    .nullable()),
  firstSubmissionYear: z.string().optional(),
});

export type EditCompanySchemaType = z.infer<typeof editCompanySchema>;
