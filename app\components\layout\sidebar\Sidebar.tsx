import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Sep<PERSON>tor,
  <PERSON><PERSON>,
  <PERSON>etContent,
} from "@netpro/design-system";
import { Form, useLocation } from "@remix-run/react";
import { LogOut, Menu } from "lucide-react";
import {
  type JSX,
  memo,
  type ReactNode,
  useState,
} from "react";
import { Profile } from "~/components/profile/Profile";
import { Logo } from "~/components/ui/branding/Logo";
import menu from "~/lib/utilities/sidebar-menu";
import { VersionDisplay } from "../version/VersionDisplay";
import MenuItemComponent from "./MenuItem";
import ParentMenu from "./ParentMenu";

  type SidebarProps = {
    children: ReactNode
  };

const MenuList = memo(({ isActive }: { isActive: (item: { href: string }) => boolean }) => {
  return (
    <li>
      <ul>
        {menu().map(
          item => item.show
            && (item.children && item.children?.length > 0
              ? (
                  <ParentMenu item={item} key={item.href} active={isActive(item)} />
                )
              : (
                  <MenuItemComponent
                    item={item}
                    key={item.href}
                    active={isActive(item)}
                  />
                )),
        )}
      </ul>
    </li>
  );
});

MenuList.displayName = "MenuList";

export function Sidebar({ children }: SidebarProps): JSX.Element | null {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const isActive = (item: { href: string }): boolean => {
    if (item.href !== "/") {
      return location.pathname.startsWith(item.href);
    }

    return location.pathname === item.href;
  };

  return (
    <>
      <div className="sticky top-0 z-40 flex items-center gap-x-6 bg-white p-4 shadow-sm sm:px-6 lg:hidden ">
        <Button variant="ghost" className="-m-2.5 p-2.5 text-gray-700 lg:hidden" onClick={() => setSidebarOpen(true)}>
          <span className="sr-only">Open sidebar</span>
          <Menu className="size-6" aria-hidden="true" />
        </Button>
        <div className="flex-1 text-sm font-semibold leading-6 text-white">Dashboard</div>
        {/* <Profile
          alignment="right"
        /> */}
      </div>

      <div>
        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <SheetContent side="left" className="p-0">
            <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-4 pt-4 pb-2">
              <div className="flex h-9 w-auto shrink-0 px-2.5 items-center gap-2.5">
                <Logo className="py-2 h-9 " />
              </div>
              <nav className="flex flex-1 flex-col">
                <ul className="flex flex-1 flex-col justify-between">
                  <MenuList isActive={isActive} />
                </ul>
                <div className="absolute bottom-2.5 left-2.5 right-2.5">
                  <div className="flex flex-col w-full gap-1">
                    <VersionDisplay className="mt-1" />
                    <Separator className="my-2.5" />
                  </div>
                </div>
              </nav>
            </div>
          </SheetContent>
        </Sheet>
      </div>

      <div className="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-0 lg:flex lg:w-[260px] lg:max-w-[260px] lg:flex-col">
        <ScrollArea className="lg:w-[260px] lg:max-w-[260px]">
          <div className="flex min-h-screen flex-col gap-y-5 border-r border-gray-200 bg-white p-1.5 lg:w-[260px] lg:max-w-[260px]">
            <div className="flex min-h-12 w-full shrink-0 items-center py-0 px-2.5 gap-2.5">
              <Logo className="h-9 py-2" />
            </div>
            <nav className="flex flex-1 flex-col">
              <ul className="flex flex-1 flex-col gap-1">
                <MenuList isActive={isActive} />
                <li className="mt-auto">
                  <div className="flex flex-col w-full p-2.5 gap-3">
                    <VersionDisplay className="mt-1" />
                    <Profile />
                    <Form method="post" action="/logout" className="place-self-end">
                      <Button type="submit" variant="outline" className="gap-2">
                        <LogOut size={16} className="text-blue-600" />
                        Logout
                      </Button>
                    </Form>
                  </div>
                </li>
              </ul>
            </nav>
            <ScrollBar />
          </div>
        </ScrollArea>
      </div>

      {children}
    </>
  );
}
