import * as AlertDialog from "@radix-ui/react-alert-dialog";
import { withClassName } from "~/lib/with-class-name";

// eslint-disable-next-line react-refresh/only-export-components -- Allow this export
export * from "@radix-ui/react-alert-dialog"

export const Overlay = withClassName(AlertDialog.Overlay, "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0")

export const Content = withClassName(AlertDialog.Content, "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg")

export const Title = withClassName(AlertDialog.Title, "text-lg font-semibold leading-none tracking-tight")

export const Footer = withClassName("div" as any, "flex justify-end gap-4 mt-6");

export const Description = withClassName(AlertDialog.Description, "text-sm text-muted-foreground")
