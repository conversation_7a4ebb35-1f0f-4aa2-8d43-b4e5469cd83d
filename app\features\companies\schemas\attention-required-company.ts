import { z } from "zod";

const requiredErrorComments = "Please provide your message.";

export const attentionRequiredCompanySchema = z.object({
  recipientEmail: z.string({ required_error: "Please provide a recipient email." }).email(),
  assistanceRequestComments: z.string({ required_error: requiredErrorComments }).min(1, requiredErrorComments),
});

export type AttentionRequiredCompanySchemaType = z.infer<typeof attentionRequiredCompanySchema>;
