import type { UseFormatDateOptions } from "./useFormatDate";
import { useFormatDate } from "./useFormatDate";

type Options = {
  fallback?: string
} & UseFormatDateOptions

export function useFormatColDate() {
  const formatDate = useFormatDate();

  return function formatColDate<Row extends object>(key: keyof Row, options?: Options) {
    const { fallback = "", ...formatDateOptions } = options || {}

    return function formatter(input: Row | { row: { original: Row } }) {
      const data = "row" in input ? input.row.original : input;

      return data[key] && typeof data[key] === "string" ? formatDate(data[key], formatDateOptions) : fallback
    }
  }
}
