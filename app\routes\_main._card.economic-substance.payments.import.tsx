import type { ReactNode } from "react";
import { PaymentImportContainer } from "~/features/economic-substance-tbah/payment-import/components/PaymentImportContainer";
import { ImportPaymentProvider } from "~/features/economic-substance-tbah/payment-import/context/ImportPaymentContext";

import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";

export const handle = {
  breadcrumb: {
    label: "Bulk import",
    to: "/economic-substance/payments/import",
  },
  title: "Bulk import",
}

export const loader = makeEnhancedLoader(async ({ request }) => {
  await middleware(["auth"], request);

  return null;
}, { authorize: ["es.bahamas.payments.import"] });

export default function SimplifiedTaxReturnBulkImport(): ReactNode {
  return (
    <ImportPaymentProvider>
      <PaymentImportContainer />
    </ImportPaymentProvider>
  )
}
