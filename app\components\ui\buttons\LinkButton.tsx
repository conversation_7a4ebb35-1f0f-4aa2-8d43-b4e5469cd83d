import type { ReactNode } from "react";
import { Button, type ButtonProps } from "@netpro/design-system";
import { Link, type LinkProps } from "@remix-run/react";

type Props = {
  buttonProps?: ButtonProps
  linkProps: LinkProps
  children: ReactNode
}

export function LinkButton({ buttonProps, linkProps, children }: Props) {
  if (buttonProps?.disabled) {
    return (
      <Button
        {...buttonProps}
        disabled
      >
        {children}
      </Button>
    );
  }

  return (
    <Link {...linkProps}>
      <Button
        {...buttonProps}
      >
        {children}
      </Button>
    </Link>
  );
}
