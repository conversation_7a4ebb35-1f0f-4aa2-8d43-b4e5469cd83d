import type { ReactNode } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@netpro/design-system";
import { useLocation, useNavigate } from "@remix-run/react";
import { useLoaderData } from "@remix-run/react/dist/components";
import { createColumnHelper } from "@tanstack/react-table";
import { Plus } from "lucide-react";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { useFeeLogsColumns } from "~/features/basic-financial-report/fees/utilities/feeLogsColumns";
import { useUserHasPermission } from "~/hooks/use-user-has-permission";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { Jurisdictions } from "~/lib/utilities/jurisdictions";
import { requireActiveJurisdiction } from "~/lib/utilities/require-active-jurisdiction";
import { getJurisdictionSettings } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Fees",
    to: "/basic-financial-report/fees",
  },
  title: "Fees",
};

export const loader = makeEnhancedLoader(async ({ json, request }) => {
  await middleware(["auth"], request);
  const { jurisdiction: bfrJurisdiction } = await requireActiveJurisdiction({ request, code: Jurisdictions.PANAMA });
  const { data: feeData } = await getJurisdictionSettings({ headers: await authHeaders(request), path: { jurisdictionId: bfrJurisdiction.id || "" } })

  return json({
    bfrFee: feeData,
  })
}, { authorize: ["companies.custom-bfr-fee.view"] })

export type FeesLoaderData = typeof loader

export default function Fees(): ReactNode {
  const navigate = useNavigate();
  const location = useLocation();
  const formatColDate = useFormatColDate();
  const feeLogsColumns = useFeeLogsColumns();
  const canEditFee = useUserHasPermission({ oneOf: ["companies.custom-bfr-fee.set"] });
  const onRowClick = canEditFee ? {} : { onRowClick: () => false };
  const { bfrFee } = useLoaderData<typeof loader>();

  if (!bfrFee) {
    throw new Error("The information for Basic Financial Report Fee must be retrieved")
  }

  const hasBfrFeeSettingsData = !!bfrFee.feeSettings?.bfrSubmissionFee
  const columnHelper = createColumnHelper<{ bfrSubmissionFee: number | null | undefined, updatedAt: string | null | undefined }>();
  const columnsFeeSettings = [
    columnHelper.display({ id: "updatedAt", header: "Updated at", cell: formatColDate("updatedAt", { fallback: "N/A" }) }),
    columnHelper.display({ id: "bfrSubmissionFee", header: "Fee", cell: props => props.row.original.bfrSubmissionFee }),
  ]
  const feeData = bfrFee.feeSettings?.bfrSubmissionFee
    ? [{ updatedAt: bfrFee.logs?.[0].actionDate, bfrSubmissionFee: bfrFee.feeSettings?.bfrSubmissionFee }]
    : []

  if (!bfrFee) {
    return <div>Error: Data not loaded</div>;
  }

  return (
    <Tabs className="w-full" defaultValue="fee">
      <TabsList className="grid max-w-80 grid-cols-2 border-b-0">
        <TabsTrigger value="fee">Fee</TabsTrigger>
        <TabsTrigger value="log">Log</TabsTrigger>
      </TabsList>
      <TabsContent value="fee">
        <CardContainer>
          <Authorized oneOf={["companies.custom-bfr-fee.set"]}>
            <div>
              {!hasBfrFeeSettingsData && (
                <Button type="button" variant="default" onClick={() => navigate("/basic-financial-report/fees/create")}>
                  <Plus size={15} />
                  Set Fee
                </Button>
              )}
            </div>
          </Authorized>
          <div className="h-full my-16 overflow-hidden">
            <span className="text-xs text-gray-600 mb-4">This is the regular amount that is charged to all submissions for all years, unless they have a deviation on the company level</span>
            <EnhancedTable
              {...onRowClick}
              rowId="bfrSubmissionFee"
              sheetURL="/basic-financial-report/fees/"
              returnURL="/basic-financial-report/fees"
              data={feeData}
              columns={columnsFeeSettings}
              totalItems={hasBfrFeeSettingsData ? 1 : 0}
              showPagination={false}
              defaultOpen={/^\/basic-financial-report\/fees\/./.test(location.pathname)}
            />
          </div>
        </CardContainer>
      </TabsContent>
      <TabsContent value="log">
        <EnhancedTable
          rowId="id"
          data={bfrFee.logs}
          columns={feeLogsColumns}
          totalItems={bfrFee.logs?.length}
          showPagination={false}
        />
      </TabsContent>
    </Tabs>
  );
}
