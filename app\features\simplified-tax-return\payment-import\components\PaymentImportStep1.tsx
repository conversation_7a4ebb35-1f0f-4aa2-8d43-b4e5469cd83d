import type { ReactNode } from "react";
import { <PERSON><PERSON>, CardDescription, CardHeader, CardTitle, Dropzone, File<PERSON>ist, FileUploader, Separator } from "@netpro/design-system";
import { Link, useNavigate } from "@remix-run/react";
import ExcelJS from "exceljs";
import { ChevronRight, CloudUpload } from "lucide-react";
import { useState } from "react";
import { CardContainer } from "~/components/CardContainer";
import { useStepperContext } from "~/lib/hooks/useStepperContext";
import { CurrentStepAction } from "~/lib/types/step";
import { useImportPaymentContext } from "../hooks/use-import-payment-context";

function getRejectionMessage(rejectionCode: string): string {
  switch (rejectionCode) {
    case "file-invalid-type":
      return "Invalid file type. Only XLSX files are allowed";
    case "file-too-large":
      return "File is too large. Max size is 5MB";
    case "too-many-files":
      return "Too many files. Max 1 file allowed";
    default:
      return "Unknown error";
  }
}

export function PaymentImportStep1(): ReactNode {
  const { files, setFiles, setSheetRows } = useImportPaymentContext()
  const { onChangeStep } = useStepperContext()
  const navigate = useNavigate()
  const [errors, setErrors] = useState<string[]>([]);
  const canNavigateNextStep = files.length > 0;
  const processFile = async (newFiles: File[]): Promise<void> => {
    const uploadedFile = newFiles[0];
    setErrors([]);

    try {
      const fileData = await uploadedFile.arrayBuffer();
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(fileData);

      const sheet = workbook.worksheets[0]; // Assuming the first sheet
      if (!sheet) {
        setErrors(["The file must contain at least one sheet."]);

        return;
      }

      const rows = [];
      const sheetRows: Array<string[]> = [];
      const header: string[] = [];

      sheet.eachRow((row, rowNumber) => {
        const values = row.values as string[];
        if (rowNumber === 1) {
          header.push(...values.slice(1)); // Skip the first undefined value
        } else {
          rows.push(values.slice(1));
          sheetRows.push(values.slice(1));
        }
      });

      // Data validation
      const validationErrors: string[] = [];

      if (rows.length < 1) {
        validationErrors.push(`The file must contain more than 1 row. This file contains ${rows.length} rows.`);
      }

      sheetRows.forEach((row, index) => {
        const realIndex = index + 2; // Adjust for Excel's 1-based indexing and headers
        if (row.length !== 2 || row[0] === undefined || row[1] === undefined) {
          validationErrors.push(`Each row must have 2 columns, there are inconsistencies in row ${realIndex}.`);
        }
      });

      if (header[0] !== "VP Code") {
        validationErrors.push(`Cell A1 must be named 'VP Code'. Your current input is '${header[0]}'.`);
      }

      if (header[1] !== "FILLING YEAR / ANNUAL SUBMISSION YEAR") {
        validationErrors.push(
          `Cell B1 must be named 'FILLING YEAR / ANNUAL SUBMISSION YEAR'. Your current input is '${header[1]}'.`,
        );
      }

      if (rows.length > 2000) {
        validationErrors.push(`The file must not contain more than 2000 rows. This file contains ${rows.length} rows.`);
      }

      if (validationErrors.length > 0) {
        setErrors(validationErrors);
      }

      if (validationErrors.length === 0) {
        setFiles(newFiles);
        setSheetRows(sheetRows);
      }
    } catch (e) {
      setErrors(["Invalid file"]);
    }
  };

  return (
    <CardContainer>
      <CardHeader className="px-0 pb-1">
        <CardTitle className="text-lg">Update company payment status</CardTitle>
      </CardHeader>
      <CardDescription>
        Upload the payment status file below to start. File should adhere to the
        <Button variant="link" className="px-0 ml-1">
          <Link to="/documents/payment-template.xlsx" target="_blank">
            designated template
          </Link>
        </Button>
      </CardDescription>
      <div className="px-16 py-5">
        <FileUploader
          maxFiles={1}
          files={files}
          setFiles={processFile}
          onReject={fileRejections =>
            setErrors(fileRejections.map(rej => getRejectionMessage(rej.errors[0].code)))}
          allowedTypes={["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]}
        >
          <Dropzone className="flex h-36 flex-col gap-2 border-gray-300">
            <CloudUpload className="size-10 text-primary" />
            <div className="flex flex-col gap-1">
              <p>
                Drag and drop the Microsoft Excel import file here, or click to select files
              </p>
            </div>
          </Dropzone>
        </FileUploader>
        <FileList files={files} setFiles={setFiles} />
      </div>
      {errors.length > 0 && (
        <div className="text-red-500 text-sm mt-4">
          <h3>Errors:</h3>
          <ul className="list-disc ml-5">
            {errors.map(error => (
              <li key={error}>{error}</li>
            ))}
          </ul>
        </div>
      )}
      <div className="text-gray-500 text-sm pl-5 mt-4">
        Upload requirements
        <ul className="list-disc">
          <li>Max. of 2,000 rows</li>
          <li>Max. of 1 file .XLSX only. File must not be password protected</li>
        </ul>
      </div>
      <Separator className="my-3" />
      <div className="flex gap-2 justify-end">
        <Button variant="outline" size="sm" onClick={() => navigate("/simplified-tax-return/payments")}>Cancel</Button>
        <Button size="sm" onClick={() => onChangeStep(CurrentStepAction.NEXT)} disabled={!canNavigateNextStep}>
          Next
          <ChevronRight className="size-4 ml-2" />
        </Button>
      </div>
    </CardContainer>
  );
}
