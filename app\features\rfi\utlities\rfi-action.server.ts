import {
  unstable_createMemoryUpload<PERSON>and<PERSON> as createMemoryUpload<PERSON>and<PERSON>,
  unstable_parseMultipartFormData as parseMultipartFormData,
} from "@remix-run/server-runtime";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import type { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { middleware } from "~/lib/middlewares.server";
import type { CreateRFIDTO, ManagementRfiDocumentCreateData } from "~/services/api-generated";
import { managementRfiCreate, managementRfiDocumentCreate } from "~/services/api-generated";

type ActionArgs = Parameters<Parameters<typeof makeEnhancedAction>[0]>[0];

export async function getRfiAction(route: string, { request, redirect, params, setNotification, json }: ActionArgs) {
  await middleware(["auth"], request);

  const { id } = params;

  if (!id) {
    throw new Response("Not Found", { status: 404 });
  }

  const maxPartSize = 5 * 1024 * 1024; // 5MB in bytes
  const uploadHandler = createMemoryUploadHandler({
    maxPartSize,
  });
  const headersRfi = await authHeaders(request)
  const formData = await parseMultipartFormData(request, uploadHandler);
  const { comments, deadLine } = Object.fromEntries(formData) as CreateRFIDTO
  const files = formData.getAll("files") as File[];
  const { data: rfiId, error } = await managementRfiCreate({
    headers: headersRfi,
    body: {
      submissionId: id,
      deadLine,
      comments,
      includeAttachments: files.length > 0,
    },
  })

  if (error) {
    setNotification({ title: error.exceptionMessage as string, variant: "error" })

    return json({ success: false, error: error.exceptionMessage });
  }

  if (rfiId && files.length > 0) {
    const formattedFiles: ManagementRfiDocumentCreateData["body"][] = files.map((file, index) => ({
      File: file,
      UploadComplete: index === files.length - 1,
    }));
    // Create an array of promises for all file uploads
    const uploadPromises = formattedFiles.map(formattedFile =>
      managementRfiDocumentCreate({
        headers: headersRfi,
        path: { requestForInformationId: rfiId },
        body: formattedFile,
      }),
    );

    // Wait for all uploads to complete concurrently
    await Promise.all(uploadPromises);
  }

  setNotification({ title: "Request for Information created successfully", variant: "success" })

  return redirect(`/${route}/${id}`)
}
