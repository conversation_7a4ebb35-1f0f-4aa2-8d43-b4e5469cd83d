import type { permissionName } from "~/services/api-generated"

type OneOfArg = { oneOf: permissionName[], startingWith?: never }

type StartingWithArg = { oneOf?: never, startingWith: permissionName | (string & {}) }

export type UserHasPermissionConfig = OneOfArg | StartingWithArg

/*
 * Utility function to check if the user has a specific permission or a set of permissions server-side.
 * When the validation is required for an entire loader/action, use the `authorize` option of it.
 * When the validation is done client-side, use the `useUserHasPermission` hook.
 */
export function userHasPermission(permissions: string[] | null | undefined, { oneOf, startingWith }: UserHasPermissionConfig) {
  if (oneOf != null) {
    return oneOf.some(value => permissions?.includes(value))
  }

  if (startingWith != null) {
    return permissions?.some(permission => permission.startsWith(startingWith))
  }

  return false
}
