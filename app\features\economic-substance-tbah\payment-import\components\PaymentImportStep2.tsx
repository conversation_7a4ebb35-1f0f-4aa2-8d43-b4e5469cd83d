import type { ReactNode } from "react";
import { <PERSON><PERSON>, CardDescription, CardHeader, CardTitle, Separator } from "@netpro/design-system";
import { useFetcher } from "@remix-run/react";
import { getCoreRowModel, getPaginationRowModel } from "@tanstack/react-table";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect } from "react";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { companyFilingYearsSchemaClient } from "~/features/simplified-tax-return/schemas/companyFilingYearsSchema";
import { importPaymentColumns } from "~/features/simplified-tax-return/utilities/payment-import-columns";
import { useStepperContext } from "~/lib/hooks/useStepperContext";
import { CurrentStepAction } from "~/lib/types/step";
import type { SubmissionsPaidStatusResponse } from "~/services/api-generated";
import { useImportPaymentContext } from "../hooks/use-import-payment-context";

export default function PaymentImportStep2(): ReactNode {
  const fetcher = useFetcher<SubmissionsPaidStatusResponse>()
  const { tableData, setTableData, sheetRows } = useImportPaymentContext()
  const { onChangeStep } = useStepperContext()
  const handleBackNavigation = (): void => {
    setTableData(undefined)
    onChangeStep(CurrentStepAction.BACK)
  }

  useEffect(() => {
    if (sheetRows) {
      const body = { companyFilingYears: companyFilingYearsSchemaClient.safeParse(sheetRows).data }
      fetcher.submit({ data: JSON.stringify(body) }, { action: "/economic-substance/payment-management/payment-status", method: "post" })
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (fetcher.data && fetcher.data.paidStatuses) {
      setTableData({ totalItemCount: fetcher.data.paidStatuses.length, hasNext: false, hasPrevious: false, pageCount: 1, pageSize: 10, pageNumber: 1, data: fetcher.data.paidStatuses })
    }
  }, [fetcher.data, setTableData])

  return (
    <CardContainer>
      <CardHeader className="px-0 pb-1">
        <CardTitle className="text-lg">Confirm uploaded data set</CardTitle>
      </CardHeader>
      <CardDescription>Please review the uploaded data set below</CardDescription>
      <EnhancedTable
        data={tableData?.data}
        loading={<LoadingState isLoading={fetcher.state === "loading" || fetcher.state === "submitting"} />}
        rowId="companyVPCode"
        columns={importPaymentColumns}
        totalItems={tableData?.totalItemCount}
        reactTableOptions={{
          getRowId: row => `${row.companyVPCode}-${row.financialYear}`, // this must be done because the API response doesn't retrieve and Id, and the "rowId" companyCode can repeat
          getCoreRowModel: getCoreRowModel(),
          getPaginationRowModel: getPaginationRowModel(),
        }}
        showPagination={false}
      />
      <Separator className="my-3" />
      <div className="flex gap-2 justify-end">
        <Button variant="outline" size="sm" onClick={handleBackNavigation}>
          <ChevronLeft className="size-4 mr-2" />
          Back
        </Button>
        <Button size="sm" onClick={() => onChangeStep(CurrentStepAction.NEXT)}>
          Submit updated records
          <ChevronRight className="size-4 ml-2" />
        </Button>
      </div>
    </CardContainer>
  );
}
