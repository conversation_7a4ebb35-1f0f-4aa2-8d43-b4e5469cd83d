import { z } from "zod";
import { nonEmptyString, preprocessArray, stringBoolean } from "~/lib/utilities/zod-validators";

export const FORM_ID = "premise-form"
export const premiseSchema = z.object({
  addressLine1: nonEmptyString("Address Line 1"),
  addressLine2: z.string().optional(),
  country: nonEmptyString("Country"),
})

export type PremiseSchemaType = z.infer<typeof premiseSchema>

export const premisesSchema = z.object({
  bahamasPremisesOwnership: stringBoolean(),
  premises: preprocessArray(z.array(premiseSchema)).optional(),
})
  .refine((data) => {
    if (data.bahamasPremisesOwnership === "true" && (!data.premises || data.premises.length === 0)) {
      return false
    }

    return true;
  }, {
    path: ["premises", 0],
    message: "At least one item is required",
  })

export type PremisesSchemaType = z.infer<typeof premisesSchema>

export function getPremisesDefaultValues(data: PremisesSchemaType | undefined): PremisesSchemaType {
  return {
    bahamasPremisesOwnership: (data?.bahamasPremisesOwnership ?? undefined) as "true" | "false", // bypass to set as undefined the first status
    premises: data?.premises ?? [],
  };
}
