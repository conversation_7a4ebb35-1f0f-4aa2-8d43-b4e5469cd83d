import { Textarea } from "@netpro/design-system";
import { makeFormField } from "~/lib/makeFormField";

type Props = {
  textareaProps?: React.ComponentProps<typeof Textarea>
}

export const FormTextarea = makeFormField<Props>({ displayName: "FormTextarea", render: ({ field, fieldState, textareaProps }) => {
  return (
    <Textarea invalid={!!fieldState.error} {...field} {...textareaProps} />
  )
} })
