import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { zodResolver } from "@hookform/resolvers/zod";
import { redirect } from "@remix-run/react";
import { getValidatedFormData } from "remix-hook-form";
import type { UserInviteSchemaType } from "~/features/master-clients/schemas/invite-users";
import { userInviteSchema } from "~/features/master-clients/schemas/invite-users";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { middleware } from "~/lib/middlewares.server";
import { sendUserInvitations } from "~/services/api-generated";

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse | never> {
  await middleware(["auth"], request);

  return redirect("/master-clients");
}

export const action = makeEnhancedAction(async ({ json, request, params }) => {
  await middleware(["auth"], request);
  // Validate the form data and return any errors.
  const { errors, data } = await getValidatedFormData<UserInviteSchemaType>(request, zodResolver(userInviteSchema));
  if (errors) {
    return json({ errors });
  }

  const result = await sendUserInvitations({
    headers: await authHeaders(request),
    body: {
      userMasterClients: data.userIds.map(userId => ({
        userId,
        masterClientId: params.id as string,
      })),
    },
  });

  if ("error" in result && result.error) {
    return json({
      errors: {
        userIds: result.error.exceptionMessage as string,
      },
    });
  }

  return json({
    success: true,
    userIds: data?.userIds,
  });
}, { authorize: ["masterclients.send-invitation"] });
