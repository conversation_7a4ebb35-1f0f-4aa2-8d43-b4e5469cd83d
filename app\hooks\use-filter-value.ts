import { useSearchParams } from "@remix-run/react";

export type FilterFunctions = [
  (key: string, defaultValue?: string) => string,
  (key: string, value: string) => void,
];

export function useFilterValue(): FilterFunctions {
  const [searchParams, setSearchParams] = useSearchParams()
  const setFilterOption = (key: string, value: string): void => {
    setSearchParams((prev) => {
      if (!value) {
        prev.delete(key);

        return prev;
      }

      prev.set(key, value);

      return prev;
    });
  }
  const getFilterOption = (key: string, defaultValue: string = ""): string => {
    return searchParams.get(key) || defaultValue;
  }

  return [getFilterOption, setFilterOption];
}
