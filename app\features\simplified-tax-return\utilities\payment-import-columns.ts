import type { ColumnDef } from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import type { SubmissionPaidStatusDto } from "~/services/api-generated";

const columnHelper = createColumnHelper<SubmissionPaidStatusDto>();

export const importPaymentColumns: ColumnDef<SubmissionPaidStatusDto>[] = [
  columnHelper.display({
    id: "companyVPCode",
    header: "VP Code",
    cell: props => props.row.original.companyVPCode,
  }),
  columnHelper.display({
    id: "isPaid",
    header: "Status",
    cell: ({ row: { original: { isPaid } } }) => isPaid === true ? "PAID" : isPaid === false ? "UNPAID" : "",
  }),
  columnHelper.display({
    id: "financialYear",
    header: "Financial Year",
    cell: props => props.row.original.financialYear,
  }),
  columnHelper.display({
    id: "submissionAvailable",
    header: "Submission available?",
    cell: props => props.row.original.submissionAvailable ? "Yes" : "No",
  }),
]
