import { useLoaderData } from "@remix-run/react";
import type { EmployeesSchemaType } from "~/lib/economic-substance/types/employee-schema";
import type { PageSlug } from "~/lib/economic-substance/utilities/form-pages";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.submissions.$id.summary";
import { SummaryTable } from "../table/SummaryTable";
import { SummaryTableData } from "../table/SummaryTableData";
import { SummaryTableRow } from "../table/SummaryTableRow";

export function Employees({ page }: { page: PageSlug }) {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { totalEmployeesEntity, totalEmployeesRelevantActivity, totalEmployeesBahamas, employees } = submissionData[page] as EmployeesSchemaType

  return (
    <div className="space-y-5">
      <h2 className="text-blue-500 font-thin mb-4 text-lg">2. Employees</h2>
      <SummaryTable>
        <tbody>
          <SummaryTableRow>
            <SummaryTableData>
              Total number of employees of the entity.
            </SummaryTableData>
            <SummaryTableData>{totalEmployeesEntity}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Total number of employees engaged in the relevant activity
            </SummaryTableData>
            <SummaryTableData>{totalEmployeesRelevantActivity}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Total number of employees engaged in the relevant activity physically
              present in the Bahamas.
            </SummaryTableData>
            <SummaryTableData>{totalEmployeesBahamas}</SummaryTableData>
          </SummaryTableRow>
        </tbody>
      </SummaryTable>
      <SummaryTable>
        <thead>
          <SummaryTableRow>
            <SummaryTableData>
              <p className="font-bold">Full Name</p>
            </SummaryTableData>
            <SummaryTableData><p className="font-bold">Qualification</p></SummaryTableData>
            <SummaryTableData><p className="font-bold">Years of relevant experience </p></SummaryTableData>
            <SummaryTableData><p className="font-bold">Contact type</p></SummaryTableData>
          </SummaryTableRow>
        </thead>
        <tbody>
          {employees.map(e => (
            <SummaryTableRow key={`${e.fullName}-${e.qualification}`}>
              <SummaryTableData>
                {e.fullName}
              </SummaryTableData>
              <SummaryTableData>{e.qualification}</SummaryTableData>
              <SummaryTableData>{e.yearsOfExperience}</SummaryTableData>
              <SummaryTableData>{e.contractType}</SummaryTableData>
            </SummaryTableRow>
          ))}
        </tbody>
      </SummaryTable>
    </div>
  )
}
