import type { ReactNode } from "react";
import { Table, TableBody, TableCell, TableRow } from "@netpro/design-system";
import { SummaryTableCell } from "~/components/pages/SummaryTableCell";
import type { ContactInformationType } from "~/features/simplified-tax-return/schemas/contact-information/2019-2024/contact-information-schema";
import { Pages } from "~/features/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/features/submissions/context/use-submission";
import { getCountryName } from "~/lib/utilities/countries";

export function ContactInformationSummary(): ReactNode {
  const { submissionData } = useSubmission();
  const contactInformation = submissionData[Pages.CONTACT_INFORMATION] as ContactInformationType;

  return (
    <>
      <section id="client-information-section">
        <h2 className="text-lg font-semibold">Client Information</h2>

        <Table className="border border-blue-600 pointer-events-none">
          <TableBody>
            <TableRow className="w-1/2 border-0">
              <SummaryTableCell
                label="Name"
                value={contactInformation.name}
              />
              <SummaryTableCell
                label="Position"
                value={contactInformation.position}
              />
            </TableRow>
            <TableRow className="border-0">
              <SummaryTableCell
                label="Address #1"
                value={contactInformation.address1}
                colSpan={2}
              />
            </TableRow>
            <TableRow className="border-0">
              <SummaryTableCell
                label="Address #2"
                value={contactInformation.address2}
                colSpan={2}
              />
            </TableRow>

            <TableRow className="w-1/2 border-0">
              <SummaryTableCell
                label="Zip Code"
                value={contactInformation.zipCode}
              />

              <SummaryTableCell
                label="Country"
                value={getCountryName(contactInformation.country)}
              />
            </TableRow>

            <TableRow className="w-1/2 border-0">
              <SummaryTableCell
                label="Telephone"
                value={`${contactInformation.telephone.prefix} ${contactInformation.telephone.number}`}
              />
              <SummaryTableCell
                label="Fax"
                value={`${contactInformation.fax.prefix} ${contactInformation.fax.number}`}
              />
            </TableRow>

            <TableRow className="border-0">
              <SummaryTableCell
                label="Email"
                value={contactInformation.email}
                colSpan={2}
              />
              <TableCell></TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </section>

      <section id="representative-information-section">
        <h2 className="text-lg font-semibold">Registered Agent Information</h2>

        <Table className="border border-blue-600 pointer-events-none">
          <TableBody>
            <TableRow className="border-0">
              <SummaryTableCell
                label="Name"
                value={contactInformation.companyRepresentativeName}
                colSpan={2}
              />
            </TableRow>
            <TableRow className="w-1/2 border-0">
              <SummaryTableCell
                label="Telephone"
                value={`${contactInformation.companyRepresentativeTelephone.prefix} ${contactInformation.companyRepresentativeTelephone.number}`}
              />
              <SummaryTableCell
                label="Fax"
                value={`${contactInformation.companyRepresentativeFax.prefix} ${contactInformation.companyRepresentativeFax.number}`}
              />
            </TableRow>
            <TableRow className=" border-0">
              <SummaryTableCell
                label="Email"
                value={contactInformation.companyRepresentativeEmail}
                colSpan={2}
              />
              <TableCell></TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </section>
    </>
  )
}
