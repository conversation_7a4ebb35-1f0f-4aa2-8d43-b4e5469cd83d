import { Button } from "@netpro/design-system";

import * as AlertDialog from "~/components/AlertDialog";
import { usePreserveQueryNavigate } from "~/lib/hooks/usePreserveQueryNavigate";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";

export const action = makeEnhancedAction(async ({ redirect, setNotification }) => {
  setNotification({ title: "Submissions have successfully been marked as paid" })

  return redirect("/economic-substance/payments")
})

export default function EconomicSubstanceSubmissionsPaymentsMarkAsPaid(): JSX.Element {
  const navigate = usePreserveQueryNavigate()

  return (
    <AlertDialog.Root open onOpenChange={() => navigate("/economic-substance/payments/")}>
      <AlertDialog.Portal>
        <AlertDialog.Overlay />
        <AlertDialog.Content>
          <AlertDialog.Title>
            Mark as paid
          </AlertDialog.Title>
          <AlertDialog.Description className="text-md text-black">
            Are you sure you want to mark the submissions as paid
          </AlertDialog.Description>
          <AlertDialog.Footer>
            <AlertDialog.Cancel asChild>
              <Button onClick={() => navigate("/economic-substance/payments")} variant="outline" size="sm">
                Cancel
              </Button>
            </AlertDialog.Cancel>
            <Button type="submit" size="sm">
              Mark as paid
            </Button>
          </AlertDialog.Footer>
        </AlertDialog.Content>
      </AlertDialog.Portal>
    </AlertDialog.Root>
  )
}
