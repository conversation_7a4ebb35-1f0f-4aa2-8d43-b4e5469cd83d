import { useLoaderData } from "@remix-run/react";
import type { EntityDetailsSchemaType } from "~/features/economic-substance-tbah/types/entity-details-schema";
import { registeredAptUnit, registeredCountry, registeredEntityStreetNumberNameCity } from "~/features/economic-substance-tbah/utilities/constants";
import { Pages } from "~/features/economic-substance-tbah/utilities/form-pages";
import { transformBooleanStringToLabel } from "~/features/economic-substance-tbah/utilities/summary";
import { getCountryName } from "~/lib/utilities/countries";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.submissions.$id.summary";
import { SummaryTable } from "./table/SummaryTable";
import { SummaryTableData } from "./table/SummaryTableData";
import { SummaryTableRow } from "./table/SummaryTableRow";

export function TaxIdentificationAddress() {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { entityId, entityTin, sameAddress, streetNumberNameCity, country, aptUnit } = submissionData[Pages.ENTITY_DETAILS] as EntityDetailsSchemaType
  const formattedEntityId = entityId ?? "N/A"
  const formattedEntityTin = entityTin ?? "N/A"

  return (
    <div>
      <h2 className="text-blue-700 font-bold mb-4">Tax Identification and Address</h2>
      <div className="space-y-6">
        <SummaryTable>
          <tbody>
            <SummaryTableRow>
              <SummaryTableData>
                Entity Unique ID
              </SummaryTableData>
              <SummaryTableData>{formattedEntityId}</SummaryTableData>
            </SummaryTableRow>
            <SummaryTableRow>
              <SummaryTableData>
                Entity Taxpayer Identification Number (TIN), if applicable
              </SummaryTableData>
              <SummaryTableData>{formattedEntityTin}</SummaryTableData>
            </SummaryTableRow>
            <SummaryTableRow>
              <SummaryTableData>
                Physical Business Address is same as registered address?
              </SummaryTableData>
              <SummaryTableData>{transformBooleanStringToLabel(sameAddress)}</SummaryTableData>
            </SummaryTableRow>
          </tbody>
        </SummaryTable>
        <SummaryTable>
          <thead className="font-bold">
            <SummaryTableRow>
              <SummaryTableData>Physical Business Address</SummaryTableData>
              <SummaryTableData>Registered Address</SummaryTableData>
            </SummaryTableRow>
          </thead>
          <tbody>
            <SummaryTableRow>
              <SummaryTableData>
                <div>
                  Street Name, Number and City:
                  <p className="font-semibold">{streetNumberNameCity}</p>
                </div>
              </SummaryTableData>
              <SummaryTableData>
                <div>
                  Street Name, Number and City:
                  <p className="font-semibold">{registeredEntityStreetNumberNameCity}</p>
                </div>
              </SummaryTableData>
            </SummaryTableRow>
            <SummaryTableRow>
              <SummaryTableData>
                <div className="flex gap-1">
                  <div className="flex gap-2">
                    Apt/Unit:
                    <p className="font-semibold">{aptUnit}</p>
                  </div>
                  <div className="flex gap-2">
                    Country:
                    <p className="font-semibold">{getCountryName(country)}</p>
                  </div>
                </div>
              </SummaryTableData>
              <SummaryTableData>
                <div className="flex gap-1">
                  <div className="flex gap-2">
                    Apt/Unit:
                    <p className="font-semibold">{registeredAptUnit}</p>
                  </div>
                  <div className="flex gap-2">
                    Country:
                    <p className="font-semibold">{getCountryName(registeredCountry)}</p>
                  </div>
                </div>
              </SummaryTableData>
            </SummaryTableRow>
          </tbody>
        </SummaryTable>
      </div>
    </div>
  )
}
