import { useContext } from "react"
import { ContextUser } from "~/components/ContextUser"
import type { UserHasPermissionConfig } from "~/lib/utilities/user-has-permission";
import { userHasPermission } from "~/lib/utilities/user-has-permission"

/**
 * This hook is intended to be used client side when the <Authorize> component to hide elements is not
 * the desired approach. It is used to check if the user has a specific permission or a set of permissions.
 * For the server side, use the `userHasPermission` function directly.
 */
export function useUserHasPermission(args: UserHasPermissionConfig) {
  const { permissions } = useContext(ContextUser)

  return userHasPermission(permissions, args);
}
