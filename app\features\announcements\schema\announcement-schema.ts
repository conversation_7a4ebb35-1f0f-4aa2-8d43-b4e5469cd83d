import { z } from "zod";
import { createFileSchema } from "~/features/basic-financial-report/types/file-schema";
import { nonEmptyString, nonNullDate, stringBoolean } from "~/lib/utilities/zod-validators";

const requiredField = "This field"
export const announcementSchema = z.object({
  subject: nonEmptyString(requiredField),
  emailSubject: nonEmptyString(requiredField),
  sendNow: stringBoolean(),
  scheduledDate: nonNullDate(requiredField).optional(),
  scheduledTime: nonEmptyString(requiredField, true),
  sendToAllMasterClients: stringBoolean(),
  jurisdictionId: nonEmptyString(requiredField, true),
  masterClientCodes: z.array(z.string()).optional(),
  body: nonEmptyString(requiredField),
  files: createFileSchema({ optional: true }),
})
  .refine((data) => {
    // If sending now, don't require scheduled fields
    if (data.sendNow === "true") {
      return true;
    }

    // If not sending now, require scheduledDate
    return data.scheduledDate !== undefined;
  }, {
    path: ["scheduledDate"],
    message: "This field is required.",
  })
  .refine((data) => {
    // If sending now, don't require scheduled fields
    if (data.sendNow === "true") {
      return true;
    }

    // If not sending now, require scheduledTime
    return data.scheduledTime !== undefined && data.scheduledTime !== "";
  }, {
    path: ["scheduledTime"],
    message: "This field is required.",
  })
  .refine((data) => {
    if (data.sendNow === "true") {
      return true;
    }

    if (data.scheduledDate && data.scheduledTime) {
      // Parse the time string to extract hours and minutes
      const [hours, minutes] = data.scheduledTime.split(":").map(Number); // Combine date and time into a single Date object
      const scheduledDateTime = new Date(data.scheduledDate);
      scheduledDateTime.setHours(hours, minutes, 0, 0);

      // Ensure the combined date-time is in the future
      return scheduledDateTime > new Date();
    }

    return false; // Fails if scheduledDate or scheduledTime is missing
  }, {
    path: ["scheduledTime"],
    message: "The scheduled time must be in the future.",
  })
  .refine((data) => {
    return !(data.sendToAllMasterClients === "true" && !data.jurisdictionId)
  }, {
    path: ["jurisdictionId"],
    message: "This field is required.",
  })
  .refine((data) => {
    return !(data.sendToAllMasterClients === "false" && (!data.masterClientCodes || data.masterClientCodes.length === 0))
  }, {
    path: ["masterClientCodes"],
    message: "This field is required.",
  })

export type AnnouncementSchemaType = z.infer<typeof announcementSchema>
