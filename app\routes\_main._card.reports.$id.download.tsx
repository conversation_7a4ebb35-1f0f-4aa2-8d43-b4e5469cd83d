import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementDownloadReport } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, params }) => {
  await middleware(["auth"], request);
  const reportsResponse = await managementDownloadReport({
    headers: await authHeaders(request),
    path: { reportId: params.id! },
  });

  if (!reportsResponse.data) {
    return new Response(null, { status: 404, statusText: "Not Found" })
  }

  return new Response(reportsResponse.data, {
    headers: reportsResponse.response.headers,
  });
}, { authorize: ["str.management-information"] })
