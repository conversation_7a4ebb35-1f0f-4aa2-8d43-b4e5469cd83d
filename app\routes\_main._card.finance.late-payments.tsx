import type { LoaderFunctionArgs } from "@remix-run/node";
import type { JSX } from "react";
import { middleware } from "~/lib/middlewares.server";

export const handle = {
  breadcrumb: {
    label: "Late Payments",
    to: "/finance/late-payments",
  },
  title: "Late Payments",
}

export async function loader({ request }: LoaderFunctionArgs): Promise<null | never> {
  await middleware(["auth"], request);

  return null;
}

export default function LatePayments(): JSX.Element {
  return <div>LatePayments</div>;
}
