import type { ReactNode } from "react";
import { Button, SelectItem } from "@netpro/design-system";
import {
  json,
  redirect,
  type ShouldRevalidateFunctionArgs,
  useLoaderData,
  useLocation,
  useNavigation,
} from "@remix-run/react";
import { Filter } from "lucide-react";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { PageErrorBoundary } from "~/components/errors/PageErrorBoundary";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormSearch } from "~/components/FormSearch";
import { FormSelect } from "~/components/FormSelect";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { useCompanyColumns } from "~/features/companies/hooks/useCompanyColumns";
import { searchSchema } from "~/features/companies/schemas/pendingOnboardingsSearchSchema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import type { OnboardingStatus } from "~/services/api-generated";
import { getCompanies } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Pending Onboardings",
    to: "/companies/pending-onboardings",
  },
  title: "Pending Onboardings",
}

export const onboardingStatuses: OnboardingStatus[] = [
  "Onboarding",
  "Declined",
]

export function shouldRevalidate({ currentUrl, nextUrl, defaultShouldRevalidate }: ShouldRevalidateFunctionArgs): boolean {
  if (currentUrl.searchParams.get("columns") !== nextUrl.searchParams.get("columns")) {
    // Do not reload the action or loader data if the visible columns change.
    return false;
  }

  if (nextUrl.pathname !== currentUrl.pathname && nextUrl.pathname.includes(currentUrl.pathname)) {
    // The next URL is a sub-route of the current URL, probably the overlay. Don't reload the data in the background.
    return false;
  }

  return defaultShouldRevalidate;
}

export const loader = makeEnhancedLoader(async ({ enhancedURL, request, queryString }) => {
  await middleware(["auth"], request);
  const schemaData = searchSchema.safeParse(queryString).data;
  const { pageNumber, pageSize } = await getFilterParams({ request });

  // If the user has not selected an status, default to "Onboarding".
  if (!schemaData?.onboardingStatus) {
    return redirect(`/companies/pending-onboardings?${new URLSearchParams({ ...queryString, onboardingStatus: "Onboarding" })}`);
  }

  const { data: paginatedCompanies, error } = await getCompanies({
    headers: await authHeaders(request),
    query: {
      pageSize,
      pageNumber,
      searchTerm: schemaData?.search,
      isActive: !schemaData?.state ? undefined : schemaData?.state === "active",
      SortOrder: enhancedURL.searchParams.get("orderDirection") ?? undefined,
      SortBy: enhancedURL.searchParams.get("order") ?? undefined,
      onboardingStatus: schemaData.onboardingStatus === "All" ? onboardingStatuses : [schemaData.onboardingStatus],
    },
  });

  if (error) {
    // Unhandled API error
    console.error("Error fetching companies", error);
    throw new Response("Currently unable to retrieve companies", { status: 412 });
  }

  return json({
    paginatedCompanies,
  });
}, { authorize: ["companies.onboarding.access"] });

export default function PendingOnboardings(): ReactNode {
  const { paginatedCompanies: { data: companies, totalItemCount } } = useLoaderData<typeof loader>();
  const location = useLocation();
  const navigation = useNavigation();
  const { columns } = useCompanyColumns("pending-onboardings");
  const { formMethods } = useFilterForm(searchSchema);

  return (
    <CardContainer>
      <Form formMethods={formMethods}>
        <FilterRow cols={6}>
          <FormColumnsFilter columns={columns} />
          <FormSelect
            name="state"
            label="PCP Entity Status"
            selectValueProps={{ placeholder: "All" }}
            options={["All", "Active", "Inactive"].map(state => (
              <SelectItem key={state.toLowerCase()} value={state.toLowerCase()}>{state}</SelectItem>
            ))}
          />
          <FormSelect
            name="onboardingStatus"
            label="Onboarding Status"
            selectValueProps={{ placeholder: "Onboarding" }}
            options={["All", ...onboardingStatuses].map(state => (
              <SelectItem key={state} value={state}>{state}</SelectItem>
            ))}
          />

        </FilterRow>
        <FilterRow>
          <div className="col-span-full flex flex-row items-center gap-2">
            <FormSearch
              name="search"
              formItemProps={{ className: "w-full" }}
              inputProps={{ placeholder: "Search entity name, master client, referral office, etc." }}
            />
            <Button size="sm" className="gap-1.5" type="submit">
              <Filter size={14} />
              Apply Filter(s)
            </Button>
          </div>
        </FilterRow>
      </Form>

      <EnhancedTableContainer>
        <EnhancedTable
          sheetURL={row => `/companies/pending-onboardings/${row.id}`}
          returnURL="/companies/pending-onboardings"
          data={companies}
          loading={<LoadingState isLoading={navigation.state === "loading"} />}
          rowId="id"
          columns={columns}
          totalItems={totalItemCount}
          defaultOpen={/^\/companies\/pending-onboardings\/./.test(location.pathname)}
        />
      </EnhancedTableContainer>
    </CardContainer>
  );
}

export const ErrorBoundary = PageErrorBoundary;
